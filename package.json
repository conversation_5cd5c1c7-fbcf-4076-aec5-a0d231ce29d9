{"name": "episteme", "version": "0.1.0", "description": "AI-powered code understanding and documentation for JavaScript/TypeScript projects", "main": "index.js", "scripts": {"setup": "./scripts/setup.sh", "dev": "./scripts/dev.sh", "dev:manual": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:shared && npm run build:backend && npm run build:frontend", "build:shared": "cd shared && npm run build", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "cd backend && npm start", "install:all": "npm install && cd shared && npm install && cd ../backend && npm install && cd ../frontend && npm install", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "type-check": "npm run type-check:backend && npm run type-check:frontend", "type-check:backend": "cd backend && npx tsc --noEmit", "type-check:frontend": "cd frontend && npm run type-check", "clean": "rm -rf node_modules */node_modules */dist backend/logs data/*.db", "docker:build": "docker build -t episteme .", "docker:run": "docker run -p 3001:3001 --env-file .env episteme", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "keywords": ["code-analysis", "documentation", "ai", "typescript", "javascript"], "author": "Episteme Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["backend", "frontend", "shared"]}