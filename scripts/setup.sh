#!/bin/bash

# Episteme Setup Script
echo "🧠 Setting up Episteme..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Install dependencies
echo "📦 Installing dependencies..."
npm run install:all

# Build shared package
echo "🔨 Building shared package..."
cd shared && npm run build && cd ..

# Copy environment file
if [ ! -f .env ]; then
    echo "📝 Creating environment file..."
    cp .env.example .env
    echo "⚠️  Please edit .env file and add your OpenAI API key"
else
    echo "✅ Environment file already exists"
fi

# Create data directory
mkdir -p data
echo "✅ Created data directory"

# Check if OpenAI API key is set
if [ -f .env ]; then
    if grep -q "your_openai_api_key_here" .env; then
        echo "⚠️  Warning: Please set your OpenAI API key in .env file"
        echo "   Get your API key from: https://platform.openai.com/api-keys"
    fi
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit .env file and add your OpenAI API key"
echo "2. Run 'npm run dev' to start development servers"
echo "3. Open http://localhost:5173 in your browser"
echo ""
echo "For production deployment:"
echo "1. Run 'docker-compose up -d'"
echo "2. Open http://localhost in your browser"
