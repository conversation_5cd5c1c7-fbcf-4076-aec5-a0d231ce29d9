#!/bin/bash

# Development startup script
echo "🚀 Starting Episteme development environment..."

# Check if .env exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Run 'npm run setup' first."
    exit 1
fi

# Check if OpenAI API key is set
if grep -q "your_openai_api_key_here" .env; then
    echo "❌ OpenAI API key not set in .env file"
    echo "   Please edit .env and add your OpenAI API key"
    exit 1
fi

# Check if shared package is built
if [ ! -d "shared/dist" ]; then
    echo "📦 Building shared package..."
    cd shared && npm run build && cd ..
fi

# Start development servers
echo "🔥 Starting development servers..."
echo "   Backend: http://localhost:3001"
echo "   Frontend: http://localhost:5173"
echo ""
echo "Press Ctrl+C to stop all servers"

npm run dev
