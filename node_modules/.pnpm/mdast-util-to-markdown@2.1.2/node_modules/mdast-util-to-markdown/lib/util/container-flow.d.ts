/**
 * @import {State} from 'mdast-util-to-markdown'
 * @import {FlowChildren, FlowParents, TrackFields} from '../types.js'
 */
/**
 * @param {FlowParents} parent
 *   Parent of flow nodes.
 * @param {State} state
 *   Info passed around about the current state.
 * @param {TrackFields} info
 *   Info on where we are in the document we are generating.
 * @returns {string}
 *   Serialized children, joined by (blank) lines.
 */
export function containerFlow(parent: FlowParents, state: State, info: TrackFields): string;
import type { FlowParents } from '../types.js';
import type { State } from 'mdast-util-to-markdown';
import type { TrackFields } from '../types.js';
//# sourceMappingURL=container-flow.d.ts.map