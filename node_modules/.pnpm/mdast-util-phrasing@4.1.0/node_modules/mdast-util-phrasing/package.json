{"name": "mdast-util-phrasing", "version": "4.1.0", "description": "mdast utility to check if a node is phrasing content", "license": "MIT", "keywords": ["unist", "mdast", "mdast=util", "util", "utility", "markdown", "phrasing"], "repository": "syntax-tree/mdast-util-phrasing", "bugs": "https://github.com/syntax-tree/mdast-util-phrasing/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://draft.li)", "contributors": ["<PERSON> <<EMAIL>> (https://draft.li)", "<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/mdast": "^4.0.0", "unist-util-is": "^6.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "c8": "^9.0.0", "prettier": "^3.0.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.56.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . -qfo && prettier . -w --log-level warn && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"prettier": true}}