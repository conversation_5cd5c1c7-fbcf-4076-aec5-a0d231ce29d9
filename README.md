# Episteme

> AI-powered code understanding and documentation for JavaScript/TypeScript projects

Episteme helps developers quickly understand codebases by providing intelligent analysis, architectural insights, and auto-generated documentation.

## 🚀 Features

- **Code Analysis**: Parse and analyze JavaScript/TypeScript projects
- **Architecture Insights**: Understand project structure and dependencies
- **AI Documentation**: Generate comprehensive documentation using AI
- **Simple Interface**: Clean web UI for easy interaction
- **Fast Setup**: Get insights in minutes, not hours

## 🏗️ Tech Stack

- **Backend**: Node.js + TypeScript + Express
- **Frontend**: React + TypeScript + Vite
- **Database**: SQLite (dev) → PostgreSQL (prod)
- **AI**: OpenAI GPT-4
- **Deployment**: Docker

## 📦 Project Structure

```text
episteme/
├── backend/           # Node.js API server
├── frontend/          # React web interface
├── shared/            # Shared types and utilities
├── scripts/           # Setup and utility scripts
├── docs/              # Documentation
├── docker-compose.yml # Docker deployment
└── Dockerfile         # Container configuration
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm or yarn
- OpenAI API key

### Development Setup

1. **Quick setup**

   ```bash
   git clone <repo-url>
   cd episteme
   npm run setup
   ```

2. **Configure environment**

   ```bash
   # Edit .env file and add your OpenAI API key
   OPENAI_API_KEY=your_actual_api_key_here
   ```

3. **Start development**

   ```bash
   npm run dev
   ```

4. **Open browser**

   Navigate to `http://localhost:5173`

## 🎯 MVP Status

- [x] Project setup and structure
- [x] JavaScript/TypeScript parser
- [x] Basic web interface
- [x] Code analysis API
- [x] AI documentation generation
- [x] File upload and processing
- [x] Docker deployment
- [x] Comprehensive documentation

## 🚀 Production Deployment

### Docker (Recommended)

```bash
# Quick deploy
docker-compose up -d

# Access at http://localhost
```

### Manual Deployment

```bash
# Build for production
npm run build

# Start production server
npm start
```

See [DEPLOYMENT.md](docs/DEPLOYMENT.md) for detailed deployment guides.

## 📚 Documentation

- [Development Guide](docs/DEVELOPMENT.md) - Setup and development workflow
- [API Documentation](docs/API.md) - REST API reference
- [Deployment Guide](docs/DEPLOYMENT.md) - Production deployment options

## 🧪 Testing

```bash
# Run all tests
npm test

# Run backend tests only
npm run test:backend

# Run with coverage
cd backend && npm run test:coverage
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes with tests
4. Commit your changes (`git commit -m 'Add amazing feature'`)
5. Push to the branch (`git push origin feature/amazing-feature`)
6. Open a Pull Request

## 📈 Roadmap

- [ ] User authentication and multi-tenancy
- [ ] Support for Python, Java, Go
- [ ] Git repository integration
- [ ] Real-time collaboration
- [ ] Advanced visualization
- [ ] Code quality scoring
- [ ] Webhook support

## 🐛 Known Issues

- Large projects (>50 files) may take several minutes to analyze
- Complex TypeScript types may not be fully parsed
- AI documentation quality depends on code clarity

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- OpenAI for GPT-4 API
- Babel team for JavaScript/TypeScript parsing
- React and Node.js communities
- All contributors and testers
