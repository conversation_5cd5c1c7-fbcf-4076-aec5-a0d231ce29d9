# API Documentation

Episteme provides a RESTful API for code analysis and project management.

## Base URL

- Development: `http://localhost:3001/api`
- Production: `https://your-domain.com/api`

## Authentication

Currently, no authentication is required for the MVP. All endpoints are publicly accessible.

## Endpoints

### Projects

#### GET /projects
List all projects.

**Response:**
```json
{
  "projects": [
    {
      "id": "proj_123",
      "name": "My Project",
      "description": "A sample project",
      "file_count": 5,
      "created_at": "2025-01-15T10:00:00Z",
      "updated_at": "2025-01-15T10:00:00Z"
    }
  ]
}
```

#### GET /projects/:id
Get project details including files.

**Response:**
```json
{
  "project": {
    "id": "proj_123",
    "name": "My Project",
    "description": "A sample project",
    "created_at": "2025-01-15T10:00:00Z",
    "updated_at": "2025-01-15T10:00:00Z",
    "files": [
      {
        "id": "file_456",
        "path": "index.js",
        "content": "console.log('Hello World');",
        "language": "javascript",
        "size": 26,
        "last_modified": "2025-01-15T10:00:00Z"
      }
    ]
  }
}
```

#### POST /projects
Create a new project.

**Request:**
```json
{
  "name": "My New Project",
  "description": "Optional description",
  "files": [
    {
      "path": "index.js",
      "content": "console.log('Hello World');"
    }
  ]
}
```

**Response:**
```json
{
  "project": {
    "id": "proj_789",
    "name": "My New Project",
    "description": "Optional description",
    "fileCount": 1
  }
}
```

#### DELETE /projects/:id
Delete a project and all its files.

**Response:**
```json
{
  "message": "Project deleted successfully"
}
```

### Analysis

#### POST /analysis
Start code analysis for a project.

**Request:**
```json
{
  "projectId": "proj_123",
  "options": {
    "generateDocs": true,
    "includeArchitecture": true,
    "includeRecommendations": true
  }
}
```

**Response:**
```json
{
  "analysisId": "ana_456",
  "status": "started",
  "estimatedDuration": 30
}
```

#### GET /analysis/:id
Get analysis status and results.

**Response:**
```json
{
  "analysis": {
    "id": "ana_456",
    "projectId": "proj_123",
    "status": "completed",
    "startedAt": "2025-01-15T10:00:00Z",
    "completedAt": "2025-01-15T10:01:30Z",
    "summary": {
      "totalFiles": 5,
      "totalLines": 150,
      "languages": {
        "javascript": 80,
        "typescript": 20
      },
      "dependencies": [
        {
          "name": "express",
          "type": "production",
          "source": "index.js"
        }
      ],
      "complexity": {
        "averageComplexity": 2.5,
        "maxComplexity": 8,
        "totalFunctions": 10,
        "totalClasses": 2,
        "duplicateCodePercentage": 5
      }
    },
    "architecture": {
      "patterns": [
        {
          "name": "Express.js Server",
          "description": "Backend API server built with Express.js",
          "confidence": 0.95,
          "examples": [
            {
              "file": "server.js",
              "startLine": 1,
              "endLine": 10,
              "code": "const express = require('express');\nconst app = express();"
            }
          ]
        }
      ],
      "structure": {
        "directories": [
          {
            "name": "src",
            "path": "src",
            "type": "directory",
            "children": []
          }
        ],
        "entryPoints": ["index.js"],
        "moduleGraph": [
          {
            "from": "index.js",
            "to": "routes/api.js",
            "type": "import"
          }
        ]
      },
      "recommendations": [
        {
          "type": "maintainability",
          "title": "Add Error Handling",
          "description": "Consider adding try-catch blocks for better error handling",
          "priority": "medium",
          "effort": "low"
        }
      ]
    },
    "documentation": {
      "overview": "This is a Node.js application built with Express.js...",
      "architecture": "The application follows a modular architecture...",
      "setup": "## Setup\n\n1. Install dependencies:\n```bash\nnpm install\n```",
      "examples": [
        {
          "title": "Basic Usage",
          "description": "How to start the server",
          "code": "npm start",
          "language": "bash"
        }
      ]
    }
  }
}
```

#### GET /analysis/project/:projectId
Get the latest analysis for a specific project.

**Response:** Same as GET /analysis/:id

## Error Responses

All endpoints return errors in the following format:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message"
  }
}
```

### Common Error Codes

- `PROJECT_NOT_FOUND` (404): Project does not exist
- `ANALYSIS_NOT_FOUND` (404): Analysis does not exist
- `VALIDATION_ERROR` (400): Invalid request data
- `NO_SUPPORTED_FILES` (400): No JavaScript/TypeScript files found
- `TOO_MANY_FILES` (400): More than 100 files uploaded
- `INTERNAL_ERROR` (500): Server error

## Rate Limits

Currently no rate limits are enforced in the MVP.

## File Upload Limits

- Maximum 100 files per project
- Maximum 1MB per file
- Supported file types: .js, .jsx, .ts, .tsx, .json

## Analysis Status Values

- `pending`: Analysis is queued
- `processing`: Analysis is running
- `completed`: Analysis finished successfully
- `failed`: Analysis failed with error

## WebSocket Events (Future)

Real-time analysis progress updates will be available via WebSocket connection:

```javascript
const ws = new WebSocket('ws://localhost:3001/ws');

ws.on('analysis:progress', (data) => {
  console.log(`Analysis ${data.analysisId}: ${data.progress}%`);
});

ws.on('analysis:complete', (data) => {
  console.log(`Analysis ${data.analysisId} completed`);
});
```

## SDK Usage

### JavaScript/TypeScript

```typescript
import { EpistemeClient } from '@episteme/sdk';

const client = new EpistemeClient({
  baseURL: 'http://localhost:3001/api'
});

// Create project
const project = await client.projects.create({
  name: 'My Project',
  files: [
    { path: 'index.js', content: 'console.log("Hello");' }
  ]
});

// Start analysis
const analysis = await client.analysis.start({
  projectId: project.id,
  options: { generateDocs: true }
});

// Poll for results
const result = await client.analysis.waitForCompletion(analysis.analysisId);
```

### Python

```python
from episteme import EpistemeClient

client = EpistemeClient(base_url='http://localhost:3001/api')

# Create project
project = client.projects.create(
    name='My Project',
    files=[
        {'path': 'index.js', 'content': 'console.log("Hello");'}
    ]
)

# Start analysis
analysis = client.analysis.start(
    project_id=project['id'],
    options={'generate_docs': True}
)

# Wait for results
result = client.analysis.wait_for_completion(analysis['analysis_id'])
```

## Webhook Support (Future)

Configure webhooks to receive analysis completion notifications:

```json
{
  "url": "https://your-app.com/webhooks/episteme",
  "events": ["analysis.completed", "analysis.failed"],
  "secret": "webhook_secret"
}
```

Webhook payload:
```json
{
  "event": "analysis.completed",
  "timestamp": "2025-01-15T10:01:30Z",
  "data": {
    "analysisId": "ana_456",
    "projectId": "proj_123",
    "status": "completed"
  }
}
```
