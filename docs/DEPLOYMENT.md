# Deployment Guide

This guide covers deploying Episteme to production environments.

## Quick Deploy with Docker

### Prerequisites
- <PERSON><PERSON> and Docker Compose
- OpenAI API key

### Steps

1. **Clone repository**
```bash
git clone <repo-url>
cd episteme
```

2. **Configure environment**
```bash
cp .env.example .env
# Edit .env and set OPENAI_API_KEY
```

3. **Deploy with Docker Compose**
```bash
docker-compose up -d
```

4. **Access application**
- Open http://localhost in your browser
- API available at http://localhost/api

## Manual Deployment

### Build for Production

```bash
# Install dependencies
npm run install:all

# Build all packages
npm run build

# Test production build
npm start
```

### Environment Setup

```bash
# Production environment variables
NODE_ENV=production
PORT=3001
DATABASE_URL=sqlite:/app/data/episteme.db
OPENAI_API_KEY=your_actual_api_key
LOG_LEVEL=info
```

### Process Management

#### Using PM2
```bash
# Install PM2
npm install -g pm2

# Start application
pm2 start backend/dist/index.js --name episteme

# Setup auto-restart
pm2 startup
pm2 save
```

#### Using systemd
```bash
# Create service file
sudo nano /etc/systemd/system/episteme.service
```

```ini
[Unit]
Description=Episteme API Server
After=network.target

[Service]
Type=simple
User=episteme
WorkingDirectory=/opt/episteme
ExecStart=/usr/bin/node backend/dist/index.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3001
EnvironmentFile=/opt/episteme/.env

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl enable episteme
sudo systemctl start episteme
```

## Cloud Deployment

### Google Cloud Platform

#### Cloud Run Deployment
```bash
# Build and push container
gcloud builds submit --tag gcr.io/PROJECT_ID/episteme

# Deploy to Cloud Run
gcloud run deploy episteme \
  --image gcr.io/PROJECT_ID/episteme \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars OPENAI_API_KEY=your_key
```

#### App Engine Deployment
```yaml
# app.yaml
runtime: nodejs18

env_variables:
  OPENAI_API_KEY: your_api_key
  NODE_ENV: production

automatic_scaling:
  min_instances: 1
  max_instances: 10
```

```bash
gcloud app deploy
```

### AWS Deployment

#### Elastic Beanstalk
```bash
# Install EB CLI
pip install awsebcli

# Initialize and deploy
eb init
eb create episteme-prod
eb deploy
```

#### ECS with Fargate
```json
{
  "family": "episteme",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "episteme",
      "image": "your-account.dkr.ecr.region.amazonaws.com/episteme:latest",
      "portMappings": [
        {
          "containerPort": 3001,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        },
        {
          "name": "OPENAI_API_KEY",
          "value": "your_api_key"
        }
      ]
    }
  ]
}
```

### Vercel Deployment

```json
// vercel.json
{
  "version": 2,
  "builds": [
    {
      "src": "backend/dist/index.js",
      "use": "@vercel/node"
    },
    {
      "src": "frontend/dist/**",
      "use": "@vercel/static"
    }
  ],
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "/backend/dist/index.js"
    },
    {
      "src": "/(.*)",
      "dest": "/frontend/dist/$1"
    }
  ],
  "env": {
    "OPENAI_API_KEY": "@openai-api-key"
  }
}
```

```bash
# Deploy to Vercel
npx vercel --prod
```

## Database Setup

### SQLite (Default)
- Suitable for small to medium deployments
- Data persisted in volume/file
- No additional setup required

### PostgreSQL (Recommended for Production)

```bash
# Update environment
DATABASE_URL=************************************/episteme

# Install PostgreSQL driver
cd backend && npm install pg @types/pg
```

Update database connection in `backend/src/database/index.ts`:

```typescript
// Add PostgreSQL support
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});
```

## Monitoring & Logging

### Health Checks
```bash
# Application health
curl http://localhost:3001/health

# Docker health check
docker ps  # Check container status
```

### Logging Setup

#### Structured Logging
```typescript
// backend/src/utils/logger.ts
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});
```

#### Log Aggregation
```bash
# Using Docker logs
docker logs episteme_episteme_1 -f

# Using journalctl (systemd)
journalctl -u episteme -f
```

## Security Considerations

### Environment Variables
- Never commit API keys to version control
- Use secrets management in production
- Rotate API keys regularly

### Network Security
```nginx
# nginx.conf additions
server {
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://backend;
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";
}
```

### SSL/TLS Setup
```bash
# Using Let's Encrypt with Certbot
sudo certbot --nginx -d yourdomain.com
```

## Performance Optimization

### Caching
```typescript
// Add Redis caching
import Redis from 'ioredis';

const redis = new Redis(process.env.REDIS_URL);

// Cache analysis results
await redis.setex(`analysis:${id}`, 3600, JSON.stringify(result));
```

### Database Optimization
```sql
-- Add indexes for better performance
CREATE INDEX idx_analyses_project_status ON analyses(project_id, status);
CREATE INDEX idx_files_project_language ON files(project_id, language);
```

## Backup & Recovery

### Database Backup
```bash
# SQLite backup
cp data/episteme.db data/episteme-backup-$(date +%Y%m%d).db

# PostgreSQL backup
pg_dump $DATABASE_URL > backup-$(date +%Y%m%d).sql
```

### Automated Backups
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
docker exec episteme_episteme_1 cp /app/data/episteme.db /app/data/backup-$DATE.db
```

## Troubleshooting

### Common Issues

#### Out of Memory
```bash
# Increase container memory
docker run -m 1g episteme

# Monitor memory usage
docker stats
```

#### High CPU Usage
```bash
# Check process usage
top -p $(pgrep node)

# Optimize analysis batch size
# Reduce concurrent analysis jobs
```

#### Database Locks
```bash
# Check SQLite locks
lsof data/episteme.db

# Restart application
docker-compose restart
```

### Monitoring Commands
```bash
# Check application status
curl -f http://localhost/health || echo "App down"

# Monitor logs
docker-compose logs -f

# Check resource usage
docker stats episteme_episteme_1
```

## Scaling

### Horizontal Scaling
- Deploy multiple instances behind load balancer
- Use shared database (PostgreSQL)
- Implement Redis for session storage

### Vertical Scaling
- Increase container memory/CPU
- Optimize database queries
- Add caching layers

For production deployments, consider:
- Load balancing
- Database clustering
- CDN for static assets
- Monitoring and alerting
- Automated backups
- Security scanning
