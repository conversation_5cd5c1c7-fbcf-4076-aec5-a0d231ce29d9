# CCL Developer Guide
## Building the Codebase Context Layer Platform

**Version:** 1.0  
**Last Updated:** January 2025  
**Audience:** CCL Platform Developers

---

## Table of Contents

1. [Getting Started](#getting-started)
2. [Development Environment](#development-environment)
3. [Architecture Overview](#architecture-overview)
4. [Coding Standards](#coding-standards)
5. [Development Workflow](#development-workflow)
6. [Testing Guidelines](#testing-guidelines)
7. [Debugging & Troubleshooting](#debugging--troubleshooting)
8. [Performance Optimization](#performance-optimization)
9. [Security Guidelines](#security-guidelines)
10. [Contributing Guidelines](#contributing-guidelines)

---

## Getting Started

### Prerequisites

Before you begin developing for CCL, ensure you have:

```bash
# Required tools and versions
- Google Cloud SDK 450.0.0+
- Docker 24.0.0+
- Node.js 20.0.0+ (LTS)
- Python 3.11+
- Rust 1.75+
- Go 1.21+
- Git 2.40+

# Development tools
- VS Code or JetBrains IDE
- Postman or similar API testing tool
- Chrome DevTools for frontend debugging
```

### Initial Setup

```bash
# 1. Clone the repository
git clone https://github.com/ccl-platform/ccl.git
cd ccl

# 2. Install development dependencies
make install-dev

# 3. Setup Google Cloud authentication
gcloud auth login
gcloud auth application-default login
gcloud config set project ccl-platform-dev

# 4. Setup local environment
cp .env.example .env.local
# Edit .env.local with your configuration

# 5. Install Git hooks
make install-hooks

# 6. Run initial build
make build

# 7. Run tests to verify setup
make test

# 8. Start local development environment
make dev
```

### Repository Structure

```
ccl/
├── .github/                    # GitHub Actions workflows
│   ├── workflows/             # CI/CD pipelines
│   └── CODEOWNERS            # Code ownership rules
│
├── services/                  # Microservices
│   ├── analysis-engine/      # Rust-based code analysis
│   ├── query-intelligence/   # Python-based NLP service
│   ├── pattern-mining/       # ML pattern detection
│   ├── marketplace/          # Go-based marketplace
│   ├── auth/                # Authentication service
│   └── gateway/             # API Gateway
│
├── packages/                 # Shared packages
│   ├── ccl-core/           # Core utilities
│   ├── ccl-proto/          # Protocol buffers
│   ├── ccl-sdk-*/          # Language SDKs
│   └── ccl-types/          # Shared TypeScript types
│
├── infrastructure/          # Infrastructure as Code
│   ├── terraform/          # Terraform configurations
│   ├── kubernetes/         # K8s manifests
│   ├── docker/            # Dockerfiles
│   └── scripts/           # Deployment scripts
│
├── web/                    # Web applications
│   ├── dashboard/         # Admin dashboard
│   ├── marketplace/       # Public marketplace
│   └── docs/             # Documentation site
│
├── tools/                 # Development tools
│   ├── cli/              # CCL CLI
│   ├── analyzer/         # Code analysis tools
│   └── generators/       # Code generators
│
├── tests/                # Test suites
│   ├── unit/            # Unit tests
│   ├── integration/     # Integration tests
│   ├── e2e/            # End-to-end tests
│   └── performance/    # Performance tests
│
└── docs/               # Documentation
    ├── api/           # API documentation
    ├── architecture/  # Architecture docs
    └── guides/        # User guides
```

---

## Development Environment

### Local Development Stack

```yaml
# docker-compose.yml for local development
version: '3.8'

services:
  # Local Spanner emulator
  spanner-emulator:
    image: gcr.io/cloud-spanner-emulator/emulator
    ports:
      - "9010:9010"
      - "9020:9020"
    environment:
      - SPANNER_EMULATOR_HOST=localhost:9010
  
  # Local Redis
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
  
  # Local PostgreSQL (for development only)
  postgres:
    image: postgres:15
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: ccl_dev
      POSTGRES_USER: ccl
      POSTGRES_PASSWORD: dev_password
    volumes:
      - postgres-data:/var/lib/postgresql/data
  
  # Jaeger for distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"  # UI
      - "14268:14268"  # Collector
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411

volumes:
  redis-data:
  postgres-data:
```

### Environment Configuration

```bash
# .env.local configuration
# Google Cloud
GOOGLE_CLOUD_PROJECT=ccl-platform-dev
GOOGLE_APPLICATION_CREDENTIALS=./service-account-dev.json

# Services URLs
ANALYSIS_ENGINE_URL=http://localhost:8001
QUERY_SERVICE_URL=http://localhost:8002
PATTERN_SERVICE_URL=http://localhost:8003
MARKETPLACE_SERVICE_URL=http://localhost:8004

# Database
SPANNER_EMULATOR_HOST=localhost:9010
SPANNER_INSTANCE=ccl-dev
SPANNER_DATABASE=development

# Redis
REDIS_HOST=localhost:6379

# Vertex AI (for local development)
VERTEX_AI_PROJECT=ccl-platform-dev
VERTEX_AI_LOCATION=us-central1
VERTEX_AI_ENDPOINT=https://us-central1-aiplatform.googleapis.com

# Authentication
JWT_SECRET=dev-secret-change-in-production
OAUTH_CLIENT_ID=dev-client-id
OAUTH_CLIENT_SECRET=dev-client-secret

# Feature Flags
ENABLE_PATTERN_MARKETPLACE=true
ENABLE_REAL_TIME_COLLAB=true
ENABLE_AI_SUGGESTIONS=true

# Logging
LOG_LEVEL=debug
LOG_FORMAT=json
```

### VS Code Configuration

```json
// .vscode/settings.json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": true,
  "python.formatting.provider": "black",
  "go.formatTool": "goimports",
  "go.lintTool": "golangci-lint",
  "rust-analyzer.cargo.features": "all",
  "[rust]": {
    "editor.defaultFormatter": "rust-lang.rust-analyzer"
  }
}
```

```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Analysis Engine",
      "type": "lldb",
      "request": "launch",
      "program": "${workspaceFolder}/services/analysis-engine/target/debug/analysis-engine",
      "args": [],
      "cwd": "${workspaceFolder}/services/analysis-engine",
      "env": {
        "RUST_LOG": "debug",
        "SPANNER_EMULATOR_HOST": "localhost:9010"
      }
    },
    {
      "name": "Query Service",
      "type": "python",
      "request": "launch",
      "module": "uvicorn",
      "args": [
        "main:app",
        "--reload",
        "--port", "8002"
      ],
      "cwd": "${workspaceFolder}/services/query-intelligence",
      "env": {
        "PYTHONPATH": "${workspaceFolder}/services/query-intelligence",
        "LOG_LEVEL": "DEBUG"
      }
    },
    {
      "name": "Debug All Services",
      "type": "compound",
      "configurations": [
        "Analysis Engine",
        "Query Service",
        "Pattern Service",
        "Marketplace Service"
      ]
    }
  ]
}
```

---

## Architecture Overview

### Service Communication

```mermaid
graph TB
    Client[Client SDK/CLI] --> Gateway[API Gateway]
    
    Gateway --> Auth[Auth Service]
    Gateway --> Analysis[Analysis Engine]
    Gateway --> Query[Query Intelligence]
    Gateway --> Pattern[Pattern Mining]
    Gateway --> Market[Marketplace]
    
    Analysis --> Spanner[(Spanner)]
    Analysis --> Storage[(Cloud Storage)]
    
    Query --> Spanner
    Query --> Vertex[Vertex AI]
    Query --> Cache[(Redis)]
    
    Pattern --> BigQuery[(BigQuery)]
    Pattern --> VertexTrain[Vertex AI Training]
    
    Market --> Spanner
    Market --> Stripe[Stripe API]
```

### Key Design Patterns

#### 1. Repository Pattern for Data Access

```typescript
// packages/ccl-core/src/repositories/base.repository.ts
export abstract class BaseRepository<T extends BaseEntity> {
  constructor(
    protected readonly db: SpannerClient,
    protected readonly tableName: string
  ) {}

  async findById(id: string): Promise<T | null> {
    const query = `SELECT * FROM ${this.tableName} WHERE id = @id`;
    const [rows] = await this.db.run({
      sql: query,
      params: { id },
    });
    
    return rows.length > 0 ? this.mapRowToEntity(rows[0]) : null;
  }

  async save(entity: T): Promise<T> {
    const data = this.mapEntityToRow(entity);
    await this.db.runTransactionAsync(async (transaction) => {
      await transaction.upsert(this.tableName, data);
      await transaction.commit();
    });
    
    return entity;
  }

  protected abstract mapRowToEntity(row: any): T;
  protected abstract mapEntityToRow(entity: T): any;
}

// Implementation example
export class PatternRepository extends BaseRepository<Pattern> {
  constructor(db: SpannerClient) {
    super(db, 'patterns');
  }

  protected mapRowToEntity(row: any): Pattern {
    return new Pattern({
      id: row.pattern_id,
      name: row.name,
      type: row.type,
      confidence: row.confidence,
      occurrences: row.occurrences,
      createdAt: row.created_at,
    });
  }

  protected mapEntityToRow(entity: Pattern): any {
    return {
      pattern_id: entity.id,
      name: entity.name,
      type: entity.type,
      confidence: entity.confidence,
      occurrences: entity.occurrences,
      created_at: entity.createdAt,
    };
  }
}
```

#### 2. Service Layer Pattern

```python
# services/query-intelligence/src/services/query_service.py
from typing import Optional, List
from dataclasses import dataclass
import logging

@dataclass
class QueryContext:
    repository_id: str
    conversation_id: Optional[str]
    user_preferences: dict
    history: List[dict]

class QueryService:
    def __init__(
        self,
        gemini_client: GeminiClient,
        repository_service: RepositoryService,
        cache_service: CacheService,
        metrics_service: MetricsService
    ):
        self.gemini = gemini_client
        self.repos = repository_service
        self.cache = cache_service
        self.metrics = metrics_service
        self.logger = logging.getLogger(__name__)
    
    async def process_query(
        self,
        query: str,
        context: QueryContext
    ) -> QueryResponse:
        """Process natural language query with context"""
        
        # Start metrics tracking
        with self.metrics.timer('query.processing_time'):
            # Check cache first
            cache_key = self._generate_cache_key(query, context)
            cached_response = await self.cache.get(cache_key)
            
            if cached_response:
                self.metrics.increment('query.cache_hit')
                return cached_response
            
            # Get repository analysis
            repo_analysis = await self.repos.get_analysis(
                context.repository_id
            )
            
            if not repo_analysis:
                raise RepositoryNotAnalyzedError(
                    f"Repository {context.repository_id} not analyzed"
                )
            
            # Build enriched context
            enriched_context = await self._enrich_context(
                query, context, repo_analysis
            )
            
            # Generate response using Gemini
            response = await self._generate_response(
                query, enriched_context
            )
            
            # Cache the response
            await self.cache.set(
                cache_key, 
                response, 
                ttl=3600  # 1 hour
            )
            
            # Track metrics
            self.metrics.increment('query.processed')
            self.metrics.histogram(
                'query.confidence_score',
                response.confidence
            )
            
            return response
    
    async def _enrich_context(
        self,
        query: str,
        context: QueryContext,
        analysis: RepositoryAnalysis
    ) -> dict:
        """Enrich context with relevant information"""
        
        # Extract key terms from query
        key_terms = await self._extract_key_terms(query)
        
        # Find relevant files
        relevant_files = await self._find_relevant_files(
            key_terms, analysis
        )
        
        # Get related patterns
        patterns = await self._find_related_patterns(
            key_terms, analysis
        )
        
        return {
            'query': query,
            'key_terms': key_terms,
            'repository': {
                'name': analysis.name,
                'languages': analysis.languages,
                'architecture': analysis.architecture_type,
            },
            'relevant_files': relevant_files[:10],
            'patterns': patterns[:5],
            'conversation_history': context.history[-5:],
            'user_preferences': context.user_preferences,
        }
```

#### 3. Event-Driven Architecture

```go
// packages/ccl-core/events/event_bus.go
package events

import (
    "context"
    "encoding/json"
    "fmt"
    "sync"
    
    "cloud.google.com/go/pubsub"
)

type EventType string

const (
    AnalysisStarted   EventType = "analysis.started"
    AnalysisCompleted EventType = "analysis.completed"
    PatternDetected   EventType = "pattern.detected"
    UserAction        EventType = "user.action"
)

type Event struct {
    ID        string                 `json:"id"`
    Type      EventType             `json:"type"`
    Timestamp int64                 `json:"timestamp"`
    Data      map[string]interface{} `json:"data"`
    Metadata  map[string]string     `json:"metadata"`
}

type EventHandler func(context.Context, Event) error

type EventBus struct {
    client      *pubsub.Client
    handlers    map[EventType][]EventHandler
    mu          sync.RWMutex
}

func NewEventBus(projectID string) (*EventBus, error) {
    ctx := context.Background()
    client, err := pubsub.NewClient(ctx, projectID)
    if err != nil {
        return nil, fmt.Errorf("failed to create pubsub client: %w", err)
    }
    
    return &EventBus{
        client:   client,
        handlers: make(map[EventType][]EventHandler),
    }, nil
}

func (eb *EventBus) Publish(ctx context.Context, event Event) error {
    topic := eb.client.Topic(string(event.Type))
    
    data, err := json.Marshal(event)
    if err != nil {
        return fmt.Errorf("failed to marshal event: %w", err)
    }
    
    result := topic.Publish(ctx, &pubsub.Message{
        Data:       data,
        Attributes: event.Metadata,
    })
    
    _, err = result.Get(ctx)
    if err != nil {
        return fmt.Errorf("failed to publish event: %w", err)
    }
    
    return nil
}

func (eb *EventBus) Subscribe(eventType EventType, handler EventHandler) {
    eb.mu.Lock()
    defer eb.mu.Unlock()
    
    eb.handlers[eventType] = append(eb.handlers[eventType], handler)
}

// Usage example
func setupEventHandlers(bus *EventBus) {
    // Handle analysis completed events
    bus.Subscribe(AnalysisCompleted, func(ctx context.Context, event Event) error {
        repoID := event.Data["repository_id"].(string)
        
        // Trigger pattern detection
        return triggerPatternDetection(ctx, repoID)
    })
    
    // Handle pattern detected events
    bus.Subscribe(PatternDetected, func(ctx context.Context, event Event) error {
        pattern := event.Data["pattern"].(map[string]interface{})
        
        // Notify interested users
        return notifyPatternSubscribers(ctx, pattern)
    })
}
```

---

## Coding Standards

### General Principles

1. **Clarity over Cleverness**: Write code that is easy to understand
2. **Consistency**: Follow established patterns within the codebase
3. **Documentation**: Comment why, not what
4. **Testing**: Write tests first (TDD) when possible
5. **Performance**: Optimize after profiling, not before

### Language-Specific Standards

#### TypeScript/JavaScript

```typescript
// ✅ Good: Clear naming, proper types, error handling
export async function analyzeRepository(
  repositoryUrl: string,
  options: AnalysisOptions = {}
): Promise<AnalysisResult> {
  // Validate input
  if (!isValidRepositoryUrl(repositoryUrl)) {
    throw new ValidationError('Invalid repository URL format');
  }
  
  const { 
    branch = 'main',
    languages = [],
    incremental = false 
  } = options;
  
  try {
    // Start analysis with proper error context
    const analysis = await startAnalysis({
      repositoryUrl,
      branch,
      languages,
      incremental,
    });
    
    logger.info('Analysis started', {
      analysisId: analysis.id,
      repository: repositoryUrl,
    });
    
    return analysis;
  } catch (error) {
    logger.error('Failed to start analysis', {
      repository: repositoryUrl,
      error: error.message,
    });
    
    throw new AnalysisError(
      `Failed to analyze repository: ${error.message}`,
      { cause: error }
    );
  }
}

// ❌ Bad: Poor naming, no types, no error handling
function analyze(url, opts) {
  var result = startAnalysis(url, opts.branch || 'main');
  return result;
}
```

#### Python

```python
# ✅ Good: Type hints, docstrings, proper error handling
from typing import List, Optional, Dict
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class Pattern:
    """Represents a detected code pattern."""
    id: str
    name: str
    type: PatternType
    confidence: float
    occurrences: int
    
    def __post_init__(self):
        """Validate pattern data after initialization."""
        if not 0 <= self.confidence <= 1:
            raise ValueError(
                f"Confidence must be between 0 and 1, got {self.confidence}"
            )

class PatternDetector:
    """Detects coding patterns in analyzed code."""
    
    def __init__(
        self, 
        ml_model: MLModel,
        pattern_db: PatternDatabase,
        cache: Optional[CacheService] = None
    ):
        """
        Initialize pattern detector.
        
        Args:
            ml_model: Machine learning model for pattern detection
            pattern_db: Database for storing patterns
            cache: Optional cache service for performance
        """
        self.model = ml_model
        self.db = pattern_db
        self.cache = cache or NullCache()
    
    async def detect_patterns(
        self,
        code_analysis: CodeAnalysis,
        min_confidence: float = 0.7
    ) -> List[Pattern]:
        """
        Detect patterns in analyzed code.
        
        Args:
            code_analysis: Analyzed code data
            min_confidence: Minimum confidence threshold
            
        Returns:
            List of detected patterns above confidence threshold
            
        Raises:
            PatternDetectionError: If detection fails
        """
        try:
            # Check cache first
            cache_key = f"patterns:{code_analysis.id}"
            cached = await self.cache.get(cache_key)
            if cached:
                return cached
            
            # Run detection
            raw_patterns = await self.model.predict(code_analysis)
            
            # Filter by confidence
            patterns = [
                p for p in raw_patterns 
                if p.confidence >= min_confidence
            ]
            
            # Store in cache
            await self.cache.set(cache_key, patterns, ttl=3600)
            
            logger.info(
                f"Detected {len(patterns)} patterns for {code_analysis.id}"
            )
            
            return patterns
            
        except Exception as e:
            logger.error(f"Pattern detection failed: {e}")
            raise PatternDetectionError(
                f"Failed to detect patterns: {str(e)}"
            ) from e

# ❌ Bad: No types, poor structure, no docs
def detect_patterns(code):
    patterns = model.predict(code)
    good_patterns = []
    for p in patterns:
        if p['confidence'] > 0.7:
            good_patterns.append(p)
    return good_patterns
```

#### Go

```go
// ✅ Good: Clear interfaces, error handling, documentation
package marketplace

import (
    "context"
    "fmt"
    "time"
    
    "github.com/ccl/ccl/pkg/errors"
    "github.com/ccl/ccl/pkg/logging"
)

// Pattern represents a code pattern in the marketplace.
type Pattern struct {
    ID          string    `json:"id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    AuthorID    string    `json:"author_id"`
    Price       int64     `json:"price_cents"`
    CreatedAt   time.Time `json:"created_at"`
}

// PatternService handles pattern-related operations.
type PatternService interface {
    // GetPattern retrieves a pattern by ID.
    GetPattern(ctx context.Context, id string) (*Pattern, error)
    
    // CreatePattern creates a new pattern.
    CreatePattern(ctx context.Context, pattern *Pattern) error
    
    // PurchasePattern handles pattern purchase.
    PurchasePattern(ctx context.Context, patternID, userID string) error
}

type patternService struct {
    repo    PatternRepository
    payment PaymentService
    logger  logging.Logger
}

// NewPatternService creates a new pattern service instance.
func NewPatternService(
    repo PatternRepository,
    payment PaymentService,
    logger logging.Logger,
) PatternService {
    return &patternService{
        repo:    repo,
        payment: payment,
        logger:  logger,
    }
}

func (s *patternService) GetPattern(
    ctx context.Context, 
    id string,
) (*Pattern, error) {
    // Validate input
    if id == "" {
        return nil, errors.NewValidationError("pattern ID is required")
    }
    
    // Add tracing
    ctx, span := tracer.Start(ctx, "PatternService.GetPattern")
    defer span.End()
    
    // Get from repository
    pattern, err := s.repo.FindByID(ctx, id)
    if err != nil {
        s.logger.ErrorContext(ctx, "failed to get pattern",
            "pattern_id", id,
            "error", err,
        )
        return nil, fmt.Errorf("get pattern: %w", err)
    }
    
    if pattern == nil {
        return nil, errors.NewNotFoundError("pattern not found")
    }
    
    return pattern, nil
}

// ❌ Bad: No context, poor error handling, no docs
func GetPattern(id string) *Pattern {
    p, _ := db.Query("SELECT * FROM patterns WHERE id = ?", id)
    return p
}
```

#### Rust

```rust
// ✅ Good: Proper error handling, lifetimes, documentation
use std::path::Path;
use thiserror::Error;
use tracing::{info, error, instrument};

/// Errors that can occur during code analysis.
#[derive(Error, Debug)]
pub enum AnalysisError {
    #[error("Failed to read file: {0}")]
    FileReadError(#[from] std::io::Error),
    
    #[error("Parse error in {file}: {message}")]
    ParseError { file: String, message: String },
    
    #[error("Unsupported language: {0}")]
    UnsupportedLanguage(String),
}

/// Result type for analysis operations.
pub type AnalysisResult<T> = Result<T, AnalysisError>;

/// Represents the analysis of a single code file.
#[derive(Debug, Clone)]
pub struct FileAnalysis {
    pub path: String,
    pub language: Language,
    pub ast: AstNode,
    pub metrics: CodeMetrics,
    pub patterns: Vec<DetectedPattern>,
}

/// Analyzes code files and extracts information.
pub struct CodeAnalyzer {
    parsers: HashMap<Language, Box<dyn Parser>>,
    pattern_detector: PatternDetector,
}

impl CodeAnalyzer {
    /// Creates a new code analyzer with the given configuration.
    pub fn new(config: AnalyzerConfig) -> Self {
        let mut parsers = HashMap::new();
        
        // Initialize language parsers
        parsers.insert(Language::Rust, Box::new(RustParser::new()));
        parsers.insert(Language::JavaScript, Box::new(JsParser::new()));
        parsers.insert(Language::Python, Box::new(PythonParser::new()));
        
        Self {
            parsers,
            pattern_detector: PatternDetector::new(config.pattern_config),
        }
    }
    
    /// Analyzes a single file and returns the analysis result.
    #[instrument(skip(self, content))]
    pub async fn analyze_file(
        &self,
        path: &Path,
        content: &str,
    ) -> AnalysisResult<FileAnalysis> {
        // Detect language
        let language = detect_language(path)
            .ok_or_else(|| AnalysisError::UnsupportedLanguage(
                path.to_string_lossy().to_string()
            ))?;
        
        info!(
            path = %path.display(),
            language = ?language,
            "Analyzing file"
        );
        
        // Get appropriate parser
        let parser = self.parsers
            .get(&language)
            .ok_or(AnalysisError::UnsupportedLanguage(
                language.to_string()
            ))?;
        
        // Parse the content
        let ast = parser.parse(content)
            .map_err(|e| AnalysisError::ParseError {
                file: path.to_string_lossy().to_string(),
                message: e.to_string(),
            })?;
        
        // Calculate metrics
        let metrics = calculate_metrics(&ast);
        
        // Detect patterns
        let patterns = self.pattern_detector.detect(&ast, language).await?;
        
        Ok(FileAnalysis {
            path: path.to_string_lossy().to_string(),
            language,
            ast,
            metrics,
            patterns,
        })
    }
}

// ❌ Bad: Unwrap, no error handling, poor structure
fn analyze(file: &str) -> Analysis {
    let content = std::fs::read_to_string(file).unwrap();
    let ast = parse(&content).unwrap();
    Analysis { ast }
}
```

### Code Review Checklist

Before submitting a PR, ensure:

- [ ] **Tests**: All tests pass, new tests added for new functionality
- [ ] **Documentation**: Code is documented, README updated if needed
- [ ] **Security**: No hardcoded secrets, input validation added
- [ ] **Performance**: No obvious performance issues, profiled if needed
- [ ] **Error Handling**: All errors handled appropriately
- [ ] **Logging**: Appropriate log levels used
- [ ] **Style**: Code follows language-specific style guide
- [ ] **Dependencies**: New dependencies justified and documented

---

## Development Workflow

### Git Workflow

```bash
# 1. Create feature branch from main
git checkout main
git pull origin main
git checkout -b feature/add-pattern-detection

# 2. Make changes following conventional commits
git add .
git commit -m "feat(patterns): add singleton pattern detection

- Add singleton pattern detector
- Include tests for Java and TypeScript
- Update pattern database schema

Closes #123"

# 3. Keep branch updated
git fetch origin
git rebase origin/main

# 4. Push and create PR
git push origin feature/add-pattern-detection
```

### Conventional Commits

```
<type>(<scope>): <subject>

<body>

<footer>
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `perf`: Performance improvements
- `test`: Test additions or modifications
- `chore`: Build process or auxiliary tool changes

Examples:
```bash
feat(analysis): add support for Ruby language parsing
fix(auth): resolve token expiration issue
docs(api): update REST API documentation
perf(query): optimize pattern search performance
```

### Pull Request Process

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Checklist
- [ ] My code follows the style guidelines
- [ ] I have performed a self-review
- [ ] I have commented my code where necessary
- [ ] I have updated the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix/feature works
- [ ] New and existing unit tests pass locally

## Screenshots (if applicable)
```

### Continuous Integration

```yaml
# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Lint TypeScript
        run: npm run lint:ts
        
      - name: Lint Python
        run: |
          pip install black flake8 mypy
          black --check services/
          flake8 services/
          mypy services/
          
      - name: Lint Go
        uses: golangci/golangci-lint-action@v3
        
      - name: Lint Rust
        run: |
          cargo fmt -- --check
          cargo clippy -- -D warnings

  test:
    runs-on: ubuntu-latest
    needs: lint
    
    services:
      spanner-emulator:
        image: gcr.io/cloud-spanner-emulator/emulator
        ports:
          - 9010:9010
          
      redis:
        image: redis:7
        ports:
          - 6379:6379
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup environment
        run: |
          echo "SPANNER_EMULATOR_HOST=localhost:9010" >> $GITHUB_ENV
          echo "REDIS_HOST=localhost:6379" >> $GITHUB_ENV
          
      - name: Run tests
        run: make test-all
        
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          files: ./coverage.xml
          flags: unittests

  security:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Run security scan
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          severity: 'CRITICAL,HIGH'
```

---

## Testing Guidelines

### Testing Strategy

```
┌─────────────────────────────────────┐
│          E2E Tests (5%)             │  - User workflows
├─────────────────────────────────────┤
│     Integration Tests (20%)         │  - Service interactions
├─────────────────────────────────────┤
│        Unit Tests (75%)             │  - Business logic
└─────────────────────────────────────┘
```

### Unit Testing

```typescript
// services/analysis-engine/tests/pattern_detector.test.ts
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { PatternDetector } from '../src/pattern_detector';
import { createMockAST } from './fixtures/ast';

describe('PatternDetector', () => {
  let detector: PatternDetector;
  
  beforeEach(() => {
    detector = new PatternDetector({
      confidenceThreshold: 0.7,
      minOccurrences: 2,
    });
  });
  
  describe('detectSingletonPattern', () => {
    it('should detect singleton pattern in TypeScript', async () => {
      // Arrange
      const ast = createMockAST(`
        class DatabaseConnection {
          private static instance: DatabaseConnection;
          
          private constructor() {}
          
          static getInstance(): DatabaseConnection {
            if (!this.instance) {
              this.instance = new DatabaseConnection();
            }
            return this.instance;
          }
        }
      `);
      
      // Act
      const patterns = await detector.detect(ast, 'typescript');
      
      // Assert
      expect(patterns).toContainEqual(
        expect.objectContaining({
          type: 'singleton',
          confidence: expect.any(Number),
          className: 'DatabaseConnection',
        })
      );
      expect(patterns[0].confidence).toBeGreaterThan(0.9);
    });
    
    it('should not detect singleton without private constructor', async () => {
      // Arrange
      const ast = createMockAST(`
        class NotASingleton {
          private static instance: NotASingleton;
          
          static getInstance(): NotASingleton {
            if (!this.instance) {
              this.instance = new NotASingleton();
            }
            return this.instance;
          }
        }
      `);
      
      // Act
      const patterns = await detector.detect(ast, 'typescript');
      
      // Assert
      const singletons = patterns.filter(p => p.type === 'singleton');
      expect(singletons).toHaveLength(0);
    });
  });
});
```

### Integration Testing

```python
# tests/integration/test_query_flow.py
import pytest
import asyncio
from unittest.mock import AsyncMock, patch

from services.query_intelligence import QueryService
from services.analysis_engine import AnalysisService

@pytest.fixture
async def services():
    """Setup integrated services for testing."""
    analysis_service = AnalysisService()
    query_service = QueryService(analysis_service)
    
    yield {
        'analysis': analysis_service,
        'query': query_service
    }
    
    # Cleanup
    await analysis_service.close()
    await query_service.close()

@pytest.mark.integration
async def test_query_after_analysis(services, sample_repository):
    """Test querying a repository after analysis completes."""
    
    # Analyze repository
    analysis = await services['analysis'].analyze_repository(
        repository_url=sample_repository.url,
        branch='main'
    )
    
    # Wait for analysis to complete
    result = await analysis.wait_for_completion(timeout=60)
    assert result.status == 'completed'
    
    # Query the analyzed repository
    response = await services['query'].ask_question(
        question="What design patterns are used?",
        repository_id=result.repository_id
    )
    
    # Verify response
    assert response.answer is not None
    assert response.confidence > 0.7
    assert len(response.sources) > 0
    assert any('pattern' in s.file for s in response.sources)

@pytest.mark.integration
async def test_pattern_detection_integration(services, sample_repository):
    """Test pattern detection across multiple services."""
    
    # Analyze repository
    analysis = await services['analysis'].analyze_repository(
        repository_url=sample_repository.url
    )
    
    result = await analysis.wait_for_completion()
    
    # Get detected patterns
    patterns = await services['analysis'].get_patterns(
        repository_id=result.repository_id
    )
    
    # Query about specific pattern
    response = await services['query'].ask_question(
        f"How is the {patterns[0].name} pattern implemented?",
        repository_id=result.repository_id
    )
    
    # Verify integration
    assert patterns[0].name.lower() in response.answer.lower()
    assert response.sources[0].file in patterns[0].files
```

### End-to-End Testing

```typescript
// tests/e2e/complete-workflow.test.ts
import { test, expect } from '@playwright/test';
import { CCLClient } from '@ccl/sdk';

test.describe('Complete Analysis Workflow', () => {
  let client: CCLClient;
  let testRepoUrl: string;
  
  test.beforeAll(async () => {
    client = new CCLClient({
      apiKey: process.env.TEST_API_KEY!,
      baseUrl: process.env.TEST_API_URL,
    });
    
    testRepoUrl = 'https://github.com/ccl-test/sample-app';
  });
  
  test('should analyze repository and answer questions', async () => {
    // Start analysis
    const analysis = await client.analyze({
      repositoryUrl: testRepoUrl,
      languages: ['typescript', 'javascript'],
    });
    
    expect(analysis.analysisId).toBeTruthy();
    expect(analysis.status).toBe('pending');
    
    // Wait for completion with timeout
    const result = await client.waitForAnalysis(analysis.analysisId, {
      timeout: 300000, // 5 minutes
      pollInterval: 5000,
    });
    
    expect(result.status).toBe('completed');
    expect(result.statistics.filesAnalyzed).toBeGreaterThan(0);
    expect(result.statistics.patternsDetected).toBeGreaterThan(0);
    
    // Query the analyzed codebase
    const response = await client.query({
      question: 'What is the main architecture pattern?',
      repositoryId: result.repositoryId,
    });
    
    expect(response.answer).toContain('architecture');
    expect(response.confidence).toBeGreaterThan(0.7);
    expect(response.sources.length).toBeGreaterThan(0);
    
    // Get patterns
    const patterns = await client.patterns.list({
      repositoryId: result.repositoryId,
      type: 'architectural',
    });
    
    expect(patterns.length).toBeGreaterThan(0);
    expect(patterns[0].confidence).toBeGreaterThan(0.8);
  });
  
  test('should handle real-time updates via WebSocket', async () => {
    const ws = client.createWebSocketConnection();
    
    const progressUpdates: any[] = [];
    
    ws.on('analysis:progress', (data) => {
      progressUpdates.push(data);
    });
    
    // Start analysis
    const analysis = await client.analyze({
      repositoryUrl: testRepoUrl,
      realTimeUpdates: true,
    });
    
    // Wait for some progress updates
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    expect(progressUpdates.length).toBeGreaterThan(0);
    expect(progressUpdates[0]).toHaveProperty('percentage');
    expect(progressUpdates[0]).toHaveProperty('currentFile');
    
    ws.close();
  });
});
```

### Performance Testing

```go
// tests/performance/load_test.go
package performance

import (
    "context"
    "testing"
    "time"
    "sync"
    
    "github.com/stretchr/testify/require"
    "github.com/ccl/ccl/pkg/client"
)

func BenchmarkAnalysisAPI(b *testing.B) {
    ctx := context.Background()
    c := client.New(client.Config{
        APIKey: testAPIKey,
        BaseURL: testBaseURL,
    })
    
    b.ResetTimer()
    
    b.Run("SingleAnalysis", func(b *testing.B) {
        for i := 0; i < b.N; i++ {
            _, err := c.StartAnalysis(ctx, &client.AnalysisRequest{
                RepositoryURL: "https://github.com/small/repo",
            })
            require.NoError(b, err)
        }
    })
    
    b.Run("ConcurrentAnalysis", func(b *testing.B) {
        b.RunParallel(func(pb *testing.PB) {
            for pb.Next() {
                _, err := c.StartAnalysis(ctx, &client.AnalysisRequest{
                    RepositoryURL: "https://github.com/small/repo",
                })
                require.NoError(b, err)
            }
        })
    })
}

func TestQueryPerformance(t *testing.T) {
    ctx := context.Background()
    c := client.New(client.Config{
        APIKey: testAPIKey,
    })
    
    // Measure query latency
    latencies := make([]time.Duration, 100)
    
    for i := 0; i < 100; i++ {
        start := time.Now()
        
        _, err := c.Query(ctx, &client.QueryRequest{
            Question: "How does authentication work?",
            RepositoryID: testRepoID,
        })
        
        latencies[i] = time.Since(start)
        require.NoError(t, err)
    }
    
    // Calculate percentiles
    p50 := percentile(latencies, 50)
    p95 := percentile(latencies, 95)
    p99 := percentile(latencies, 99)
    
    // Assert performance requirements
    require.Less(t, p50, 100*time.Millisecond, "P50 latency too high")
    require.Less(t, p95, 500*time.Millisecond, "P95 latency too high")
    require.Less(t, p99, 1*time.Second, "P99 latency too high")
}
```

---

## Debugging & Troubleshooting

### Logging Configuration

```yaml
# config/logging.yaml
version: 1
formatters:
  json:
    class: pythonjsonlogger.jsonlogger.JsonFormatter
    format: '%(asctime)s %(name)s %(levelname)s %(message)s'
    
  detailed:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'

handlers:
  console:
    class: logging.StreamHandler
    level: DEBUG
    formatter: json
    stream: ext://sys.stdout
    
  file:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: detailed
    filename: logs/ccl.log
    maxBytes: 10485760  # 10MB
    backupCount: 5

loggers:
  ccl:
    level: DEBUG
    handlers: [console, file]
    propagate: false
    
  ccl.analysis:
    level: INFO
    
  ccl.query:
    level: DEBUG

root:
  level: WARNING
  handlers: [console]
```

### Debugging Tools

```bash
# Debug service locally with detailed logging
LOG_LEVEL=debug go run ./services/marketplace/... 

# Debug Python service with remote debugger
python -m debugpy --listen 5678 --wait-for-client -m uvicorn main:app

# Debug Rust with LLDB
rust-lldb target/debug/analysis-engine

# Trace HTTP requests
export GOOGLE_CLOUD_ENABLE_TRACING=true
export TRACE_SAMPLING_RATIO=1.0
```

### Common Issues

#### 1. Spanner Connection Issues

```bash
# Check emulator is running
curl http://localhost:9010/v1/projects/test-project/instances

# Reset emulator
docker-compose down spanner-emulator
docker-compose up -d spanner-emulator

# Verify environment variable
echo $SPANNER_EMULATOR_HOST
```

#### 2. Authentication Failures

```typescript
// Debug auth issues
import { GoogleAuth } from 'google-auth-library';

async function debugAuth() {
  const auth = new GoogleAuth({
    scopes: ['https://www.googleapis.com/auth/cloud-platform'],
  });
  
  try {
    const client = await auth.getClient();
    const projectId = await auth.getProjectId();
    
    console.log('Auth client type:', client.constructor.name);
    console.log('Project ID:', projectId);
    
    // Test token
    const token = await client.getAccessToken();
    console.log('Token obtained:', !!token);
    
  } catch (error) {
    console.error('Auth error:', error);
    console.log('GOOGLE_APPLICATION_CREDENTIALS:', 
      process.env.GOOGLE_APPLICATION_CREDENTIALS
    );
  }
}
```

#### 3. Memory Leaks

```go
// Profile memory usage
import (
    _ "net/http/pprof"
    "runtime"
)

func setupProfiling() {
    // Enable profiling endpoint
    go func() {
        log.Println(http.ListenAndServe("localhost:6060", nil))
    }()
    
    // Log memory stats periodically
    go func() {
        ticker := time.NewTicker(30 * time.Second)
        defer ticker.Stop()
        
        for range ticker.C {
            var m runtime.MemStats
            runtime.ReadMemStats(&m)
            
            log.Printf("Memory: Alloc=%vMB, Sys=%vMB, NumGC=%v\n",
                m.Alloc/1024/1024,
                m.Sys/1024/1024,
                m.NumGC,
            )
        }
    }()
}

// Analyze with: go tool pprof http://localhost:6060/debug/pprof/heap
```

### Distributed Tracing

```python
# Enable tracing in services
from opentelemetry import trace
from opentelemetry.exporter.jaeger import JaegerExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor

def setup_tracing(service_name: str):
    # Create Jaeger exporter
    jaeger_exporter = JaegerExporter(
        agent_host_name="localhost",
        agent_port=6831,
    )
    
    # Create tracer provider
    provider = TracerProvider()
    processor = BatchSpanProcessor(jaeger_exporter)
    provider.add_span_processor(processor)
    
    # Set global tracer provider
    trace.set_tracer_provider(provider)
    
    return trace.get_tracer(service_name)

# Use in service
tracer = setup_tracing("query-intelligence")

@app.post("/query")
async def handle_query(request: QueryRequest):
    with tracer.start_as_current_span("handle_query") as span:
        span.set_attribute("query.text", request.query)
        span.set_attribute("repository.id", request.repository_id)
        
        # Process query
        result = await process_query(request)
        
        span.set_attribute("response.confidence", result.confidence)
        return result
```

---

## Performance Optimization

### Profiling Guidelines

```go
// CPU Profiling
func profileCPU() {
    f, err := os.Create("cpu.prof")
    if err != nil {
        log.Fatal(err)
    }
    defer f.Close()
    
    if err := pprof.StartCPUProfile(f); err != nil {
        log.Fatal(err)
    }
    defer pprof.StopCPUProfile()
    
    // Run workload
    runWorkload()
}

// Analyze: go tool pprof cpu.prof
```

### Caching Strategy

```typescript
// Implement multi-level caching
class CacheManager {
  private l1Cache: Map<string, CacheEntry> = new Map();
  private l2Cache: RedisClient;
  private l3Cache: CloudCDN;
  
  async get<T>(key: string): Promise<T | null> {
    // Check L1 (in-memory)
    const l1Entry = this.l1Cache.get(key);
    if (l1Entry && !l1Entry.isExpired()) {
      metrics.increment('cache.l1.hit');
      return l1Entry.value as T;
    }
    
    // Check L2 (Redis)
    const l2Value = await this.l2Cache.get(key);
    if (l2Value) {
      metrics.increment('cache.l2.hit');
      this.l1Cache.set(key, new CacheEntry(l2Value, 300)); // 5 min TTL
      return JSON.parse(l2Value) as T;
    }
    
    // Check L3 (CDN)
    const l3Value = await this.l3Cache.get(key);
    if (l3Value) {
      metrics.increment('cache.l3.hit');
      await this.promoteToL2(key, l3Value);
      return l3Value as T;
    }
    
    metrics.increment('cache.miss');
    return null;
  }
}
```

### Database Query Optimization

```sql
-- Before: Slow query with full table scan
SELECT p.*, r.name, u.email
FROM patterns p
JOIN repositories r ON p.repository_id = r.id
JOIN users u ON r.user_id = u.id
WHERE p.confidence > 0.8
AND r.language = 'javascript'
ORDER BY p.created_at DESC;

-- After: Optimized with proper indexes and query hints
CREATE INDEX idx_patterns_confidence_created 
ON patterns(confidence, created_at DESC) 
WHERE confidence > 0.8;

CREATE INDEX idx_repositories_language_user 
ON repositories(language, user_id);

SELECT /*+ MERGE_JOIN(p r) USE_INDEX(p idx_patterns_confidence_created) */
  p.id, p.name, p.confidence,
  r.name as repo_name,
  u.email as user_email
FROM patterns p
INNER JOIN repositories r ON p.repository_id = r.id
INNER JOIN users u ON r.user_id = u.id
WHERE p.confidence > 0.8
AND r.language = 'javascript'
ORDER BY p.created_at DESC
LIMIT 100;
```

---

## Security Guidelines

### Secret Management

```bash
# Never commit secrets
# Use Google Secret Manager
gcloud secrets create api-key --data-file=api-key.txt

# Access in code
from google.cloud import secretmanager

def get_secret(secret_id: str) -> str:
    client = secretmanager.SecretManagerServiceClient()
    name = f"projects/{PROJECT_ID}/secrets/{secret_id}/versions/latest"
    response = client.access_secret_version(request={"name": name})
    return response.payload.data.decode("UTF-8")
```

### Input Validation

```typescript
// Always validate and sanitize input
import { z } from 'zod';
import DOMPurify from 'isomorphic-dompurify';

const RepositorySchema = z.object({
  url: z.string()
    .url()
    .regex(/^https:\/\/(github\.com|gitlab\.com|bitbucket\.org)\/[\w-]+\/[\w-]+$/),
  branch: z.string()
    .regex(/^[\w\-\.\/]+$/)
    .max(255)
    .default('main'),
  languages: z.array(z.enum(['javascript', 'typescript', 'python', 'go', 'rust']))
    .optional(),
});

export function validateRepositoryInput(input: unknown) {
  try {
    return RepositorySchema.parse(input);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ValidationError('Invalid input', error.errors);
    }
    throw error;
  }
}

// Sanitize HTML content
export function sanitizeHTML(dirty: string): string {
  return DOMPurify.sanitize(dirty, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'code', 'pre'],
    ALLOWED_ATTR: [],
  });
}
```

### Security Headers

```go
// Implement security middleware
func SecurityMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // Security headers
        c.Header("X-Content-Type-Options", "nosniff")
        c.Header("X-Frame-Options", "DENY")
        c.Header("X-XSS-Protection", "1; mode=block")
        c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
        c.Header("Content-Security-Policy", 
            "default-src 'self'; " +
            "script-src 'self' 'unsafe-inline' https://apis.google.com; " +
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
            "font-src 'self' https://fonts.gstatic.com; " +
            "img-src 'self' data: https:; " +
            "connect-src 'self' https://api.ccl.dev wss://api.ccl.dev")
        
        // Remove sensitive headers
        c.Header("Server", "")
        c.Header("X-Powered-By", "")
        
        c.Next()
    }
}
```

---

## Contributing Guidelines

### How to Contribute

1. **Find an Issue**
   - Check existing issues or create a new one
   - Comment on the issue to claim it
   - Wait for maintainer approval

2. **Development Process**
   - Fork the repository
   - Create a feature branch
   - Make your changes
   - Write/update tests
   - Update documentation
   - Submit a pull request

3. **Code Review Process**
   - Automated checks must pass
   - At least 2 approvals required
   - Address all feedback
   - Squash commits if requested

### Contributor License Agreement

All contributors must sign the CLA before their PR can be merged.

### Community Guidelines

- Be respectful and inclusive
- Follow the [Code of Conduct](CODE_OF_CONDUCT.md)
- Help others in discussions
- Share knowledge and learnings

---

## Resources

### Internal Documentation
- [Architecture Decision Records](docs/architecture/adr/)
- [API Design Guidelines](docs/api/design-guidelines.md)
- [Security Policies](docs/security/)

### External Resources
- [Google Cloud Best Practices](https://cloud.google.com/docs/enterprise/best-practices-for-enterprise-organizations)
- [Go Code Review Comments](https://github.com/golang/go/wiki/CodeReviewComments)
- [Effective Python](https://effectivepython.com/)
- [Rust Book](https://doc.rust-lang.org/book/)

### Tools
- [Pre-commit hooks](https://pre-commit.com/)
- [Conventional Commits](https://www.conventionalcommits.org/)
- [Semantic Versioning](https://semver.org/)

---

*Happy coding! Remember: good code is written once but read many times. Make it count!*