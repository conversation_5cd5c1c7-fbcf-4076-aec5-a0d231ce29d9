# Development Guide

This guide will help you set up and develop Episteme locally.

## Prerequisites

- Node.js 18+ 
- npm or yarn
- OpenAI API key

## Quick Start

1. **Clone and setup**
```bash
git clone <repo-url>
cd episteme
npm run setup
```

2. **Configure environment**
```bash
# Edit .env file and add your OpenAI API key
OPENAI_API_KEY=your_actual_api_key_here
```

3. **Start development**
```bash
npm run dev
```

4. **Open browser**
- Frontend: http://localhost:5173
- Backend API: http://localhost:3001

## Project Structure

```
episteme/
├── backend/           # Node.js + TypeScript API
├── frontend/          # React + TypeScript UI  
├── shared/            # Shared types and utilities
├── scripts/           # Setup and utility scripts
├── docs/              # Documentation
└── docker/            # Docker configuration
```

## Development Workflow

### Backend Development

```bash
cd backend
npm run dev          # Start with hot reload
npm test            # Run tests
npm run build       # Build for production
```

### Frontend Development

```bash
cd frontend  
npm run dev         # Start with hot reload
npm run build       # Build for production
npm run preview     # Preview production build
```

### Shared Package

```bash
cd shared
npm run build       # Build shared types
npm run dev         # Watch mode for development
```

## API Development

### Adding New Endpoints

1. Create route handler in `backend/src/routes/`
2. Add route to `backend/src/routes/index.ts`
3. Update API client in `frontend/src/services/api.ts`
4. Add types to `shared/src/types.ts`

### Database Changes

1. Update schema in `backend/src/database/index.ts`
2. Add migration logic if needed
3. Update related types in shared package

## Testing

### Backend Tests
```bash
cd backend
npm test                    # Run all tests
npm run test:watch         # Watch mode
npm run test:coverage      # With coverage
```

### Frontend Tests
```bash
cd frontend
npm test                   # Run tests
npm run test:watch        # Watch mode
```

## Code Quality

### Linting
```bash
npm run lint              # Lint all packages
npm run lint:fix          # Fix auto-fixable issues
```

### Type Checking
```bash
npm run type-check        # Check TypeScript types
```

## Environment Variables

### Backend (.env)
```bash
# Required
OPENAI_API_KEY=your_openai_api_key

# Optional
PORT=3001
NODE_ENV=development
DATABASE_URL=sqlite:./data/episteme.db
LOG_LEVEL=info
```

### Frontend (automatically proxied)
- API calls to `/api/*` are proxied to backend
- No additional configuration needed for development

## Debugging

### Backend Debugging
1. Use VS Code debugger with provided launch configuration
2. Or use Node.js inspector: `npm run dev:debug`

### Frontend Debugging
1. Use browser dev tools
2. React DevTools extension recommended

## Common Issues

### "Module not found" errors
```bash
# Rebuild shared package
cd shared && npm run build
```

### Database issues
```bash
# Delete and recreate database
rm -rf data/
mkdir data
```

### Port conflicts
```bash
# Change ports in .env file
PORT=3002  # Backend
# Frontend port in vite.config.ts
```

## Production Build

### Local Production Test
```bash
npm run build           # Build all packages
npm start              # Start production server
```

### Docker Build
```bash
docker build -t episteme .
docker run -p 3001:3001 -e OPENAI_API_KEY=your_key episteme
```

### Docker Compose
```bash
# Edit docker-compose.yml with your API key
docker-compose up -d
```

## Contributing

1. Create feature branch from `main`
2. Make changes with tests
3. Ensure all tests pass
4. Submit pull request

### Commit Convention
```
feat: add new analysis feature
fix: resolve parsing issue
docs: update setup guide
test: add integration tests
```

## Performance Tips

### Development
- Use `npm run dev` for hot reload
- Keep shared package built during development
- Use browser dev tools for frontend debugging

### Production
- Enable gzip compression
- Use CDN for static assets
- Monitor API response times
- Set up proper logging

## Troubleshooting

### Common Commands
```bash
# Reset everything
rm -rf node_modules */node_modules
npm run install:all

# Rebuild shared package
cd shared && npm run clean && npm run build

# Check logs
tail -f backend/logs/combined.log
```

### Getting Help
1. Check this documentation
2. Look at existing code examples
3. Check GitHub issues
4. Create new issue with reproduction steps
