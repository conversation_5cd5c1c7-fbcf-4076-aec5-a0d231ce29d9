# CCL Technical Specification
## Detailed Implementation Guide

**Version:** 1.0  
**Date:** January 2025  
**Status:** FINAL - Ready for Implementation  
**Target:** Google Cloud SDK One-Shot Development

---

## Table of Contents

1. [Development Environment Setup](#development-environment-setup)
2. [Core Services Implementation](#core-services-implementation)
3. [Language Parser Framework](#language-parser-framework)
4. [AI/ML Implementation](#aiml-implementation)
5. [Database Schemas & Models](#database-schemas--models)
6. [API Implementation](#api-implementation)
7. [Security Implementation](#security-implementation)
8. [Testing Strategy](#testing-strategy)
9. [Performance Optimization](#performance-optimization)
10. [Deployment Procedures](#deployment-procedures)

---

## Development Environment Setup

### Prerequisites

```bash
# Required Tools
- Google Cloud SDK 450.0.0+
- Docker 24.0+
- Node.js 20 LTS
- Python 3.11+
- Rust 1.75+
- Go 1.21+
- Terraform 1.6+

# Development Machine Setup
git clone https://github.com/ccl-platform/ccl.git
cd ccl

# Install dependencies
make install-deps

# Configure Google Cloud
gcloud auth login
gcloud config set project ccl-platform-prod
gcloud auth application-default login

# Enable required APIs
gcloud services enable \
  run.googleapis.com \
  cloudbuild.googleapis.com \
  spanner.googleapis.com \
  aiplatform.googleapis.com \
  storage.googleapis.com \
  bigquery.googleapis.com \
  firestore.googleapis.com \
  redis.googleapis.com \
  monitoring.googleapis.com \
  logging.googleapis.com \
  cloudtrace.googleapis.com
```

### Project Structure

```
ccl/
├── services/                    # Microservices
│   ├── analysis-engine/        # Rust code analysis service
│   ├── query-intelligence/     # Python query service
│   ├── pattern-mining/         # Python ML service
│   ├── marketplace/            # Go marketplace service
│   ├── auth/                   # Go authentication service
│   └── gateway/                # API Gateway configuration
│
├── packages/                    # Shared packages
│   ├── ccl-core/              # Core types and utilities
│   ├── ccl-proto/             # Protocol buffers
│   ├── ccl-sdk-js/            # JavaScript SDK
│   ├── ccl-sdk-python/        # Python SDK
│   └── ccl-sdk-go/            # Go SDK
│
├── infrastructure/             # Infrastructure as Code
│   ├── terraform/             # Terraform configurations
│   ├── kubernetes/            # K8s manifests (if needed)
│   └── scripts/               # Deployment scripts
│
├── web/                       # Web applications
│   ├── dashboard/             # Admin dashboard (Next.js)
│   ├── marketplace/           # Pattern marketplace (Next.js)
│   └── docs/                  # Documentation site
│
├── tools/                     # Development tools
│   ├── cli/                   # CCL CLI tool
│   ├── vscode-extension/      # VS Code extension
│   └── jetbrains-plugin/      # JetBrains plugin
│
└── tests/                     # Integration tests
    ├── e2e/                   # End-to-end tests
    ├── load/                  # Load testing
    └── security/              # Security tests
```

---

## Core Services Implementation

### 1. Analysis Engine Service (Rust)

```rust
// services/analysis-engine/src/main.rs
use actix_web::{web, App, HttpServer, Result};
use tokio::sync::RwLock;
use std::sync::Arc;

mod analyzer;
mod parser;
mod patterns;
mod storage;

#[derive(Clone)]
struct AppState {
    parsers: Arc<RwLock<parser::ParserRegistry>>,
    storage: Arc<storage::StorageClient>,
    patterns: Arc<patterns::PatternDetector>,
}

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt()
        .with_env_filter("info")
        .json()
        .init();

    // Initialize Google Cloud clients
    let storage_client = storage::StorageClient::new().await?;
    let spanner_client = storage::SpannerClient::new().await?;
    
    // Initialize parser registry with WebAssembly plugins
    let parser_registry = parser::ParserRegistry::new();
    parser_registry.load_plugins("./plugins").await?;
    
    // Initialize pattern detector
    let pattern_detector = patterns::PatternDetector::new();
    
    let app_state = AppState {
        parsers: Arc::new(RwLock::new(parser_registry)),
        storage: Arc::new(storage_client),
        patterns: Arc::new(pattern_detector),
    };

    // Start HTTP server
    HttpServer::new(move || {
        App::new()
            .app_data(web::Data::new(app_state.clone()))
            .service(
                web::scope("/api/v1")
                    .route("/analyze", web::post().to(analyze_repository))
                    .route("/health", web::get().to(health_check))
            )
            .wrap(tracing_actix_web::TracingLogger::default())
    })
    .bind(("0.0.0.0", 8080))?
    .run()
    .await
}

// services/analysis-engine/src/analyzer.rs
use tree_sitter::{Parser, Tree};
use wasmtime::{Engine, Module, Store};

pub struct CodeAnalyzer {
    engine: Engine,
    parsers: HashMap<String, LanguageParser>,
}

impl CodeAnalyzer {
    pub async fn analyze_file(
        &self,
        file_path: &str,
        content: &str,
        language: &str,
    ) -> Result<Analysis> {
        // Get appropriate parser
        let parser = self.parsers.get(language)
            .ok_or_else(|| anyhow!("Unsupported language: {}", language))?;
        
        // Parse the code
        let tree = parser.parse(content)?;
        
        // Extract AST
        let ast = self.extract_ast(&tree)?;
        
        // Detect patterns
        let patterns = self.pattern_detector.detect(&ast)?;
        
        // Extract dependencies
        let dependencies = self.extract_dependencies(&ast)?;
        
        // Generate embeddings
        let embeddings = self.generate_embeddings(content).await?;
        
        Ok(Analysis {
            file_path: file_path.to_string(),
            language: language.to_string(),
            ast,
            patterns,
            dependencies,
            embeddings,
            metrics: self.calculate_metrics(content),
        })
    }
}

// WebAssembly Language Parser Plugin Interface
#[derive(Clone)]
pub struct LanguageParser {
    module: Module,
    store: Store<()>,
}

impl LanguageParser {
    pub fn parse(&mut self, code: &str) -> Result<ParsedAST> {
        // Call WebAssembly parse function
        let instance = Instance::new(&mut self.store, &self.module, &[])?;
        let parse_fn = instance.get_typed_func::<(&str,), i32>(&mut self.store, "parse")?;
        
        let result_ptr = parse_fn.call(&mut self.store, (code,))?;
        // Extract and deserialize AST from WebAssembly memory
        
        Ok(parsed_ast)
    }
}
```

### 2. Query Intelligence Service (Python)

```python
# services/query-intelligence/main.py
from fastapi import FastAPI, HTTPException
from contextlib import asynccontextmanager
import uvicorn
from google import genai
from google.cloud import spanner
import asyncio
import logging

from .models import QueryRequest, QueryResponse
from .intelligence import QueryIntelligence
from .context import ContextManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='{"time": "%(asctime)s", "level": "%(levelname)s", "message": "%(message)s"}',
)

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    app.state.genai_client = genai.Client(
        vertexai=True,
        project="ccl-platform-prod",
        location="us-central1"
    )
    app.state.spanner_client = spanner.Client()
    app.state.intelligence = QueryIntelligence(
        app.state.genai_client,
        app.state.spanner_client
    )
    yield
    # Shutdown
    await app.state.intelligence.close()

app = FastAPI(lifespan=lifespan)

@app.post("/api/v1/query", response_model=QueryResponse)
async def handle_query(request: QueryRequest):
    """Handle natural language queries about code"""
    try:
        # Get or create conversation context
        context = await app.state.intelligence.get_context(
            request.conversation_id,
            request.repository_id
        )
        
        # Process query with Gemini
        response = await app.state.intelligence.process_query(
            query=request.query,
            context=context,
            user_id=request.user_id
        )
        
        return response
        
    except Exception as e:
        logging.error(f"Query processing failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# services/query-intelligence/intelligence.py
from google import genai
from typing import Dict, List, Optional
import json

class QueryIntelligence:
    def __init__(self, genai_client, spanner_client):
        self.genai = genai_client
        self.spanner = spanner_client
        self.context_manager = ContextManager(spanner_client)
        
    async def process_query(
        self, 
        query: str, 
        context: Dict,
        user_id: str
    ) -> QueryResponse:
        """Process user query with full context"""
        
        # Build comprehensive prompt
        prompt = await self._build_prompt(query, context)
        
        # Configure Gemini parameters
        config = genai.GenerationConfig(
            temperature=0.3,
            top_p=0.95,
            top_k=40,
            max_output_tokens=2048,
            response_modalities=["TEXT"],
        )
        
        # Generate response
        response = await self.genai.models.generate_content_async(
            model="gemini-2.0-flash",
            contents=[genai.Part.from_text(prompt)],
            generation_config=config
        )
        
        # Extract relevant code snippets
        code_snippets = await self._find_relevant_code(
            response.text, 
            context["repository_id"]
        )
        
        # Update conversation history
        await self.context_manager.update_conversation(
            context["conversation_id"],
            query,
            response.text,
            code_snippets
        )
        
        return QueryResponse(
            answer=response.text,
            code_snippets=code_snippets,
            confidence=self._calculate_confidence(response),
            suggested_questions=await self._generate_followups(response.text)
        )
    
    async def _build_prompt(self, query: str, context: Dict) -> str:
        """Build context-aware prompt for Gemini"""
        
        # Get repository analysis
        repo_analysis = await self._get_repository_analysis(
            context["repository_id"]
        )
        
        # Get relevant patterns
        patterns = await self._get_relevant_patterns(
            query, 
            context["repository_id"]
        )
        
        # Get conversation history
        history = context.get("history", [])
        
        prompt = f"""You are an expert code architect analyzing a codebase.

Repository Overview:
{json.dumps(repo_analysis, indent=2)}

Detected Patterns:
{json.dumps(patterns, indent=2)}

Conversation History:
{self._format_history(history)}

Current Question: {query}

Provide a detailed, accurate answer based on the codebase analysis. 
Include specific file references and code examples where relevant.
Focus on architectural understanding and best practices.
"""
        
        return prompt
```

### 3. Pattern Mining Service (Python)

```python
# services/pattern-mining/main.py
import tensorflow as tf
from transformers import AutoModel, AutoTokenizer
import numpy as np
from sklearn.cluster import DBSCAN
from google.cloud import aiplatform
import asyncio

class PatternMiningService:
    def __init__(self):
        # Initialize CodeBERT for code understanding
        self.tokenizer = AutoTokenizer.from_pretrained("microsoft/codebert-base")
        self.model = AutoModel.from_pretrained("microsoft/codebert-base")
        
        # Initialize Vertex AI
        aiplatform.init(project="ccl-platform-prod", location="us-central1")
        
        # Load custom pattern recognition model
        self.pattern_model = self._load_custom_model()
        
    async def mine_patterns(self, repository_id: str):
        """Mine patterns from analyzed code"""
        
        # Get all analyzed files
        files = await self._get_analyzed_files(repository_id)
        
        # Extract code representations
        embeddings = []
        metadata = []
        
        for file in files:
            # Generate embeddings for code segments
            file_embeddings = await self._generate_code_embeddings(file)
            embeddings.extend(file_embeddings)
            metadata.extend([file] * len(file_embeddings))
        
        # Cluster similar code patterns
        patterns = await self._cluster_patterns(embeddings, metadata)
        
        # Generate pattern templates
        templates = await self._generate_templates(patterns)
        
        # Calculate pattern quality scores
        scored_patterns = await self._score_patterns(patterns, templates)
        
        # Store patterns
        await self._store_patterns(repository_id, scored_patterns)
        
        return scored_patterns
    
    async def _generate_code_embeddings(self, file_data: Dict) -> List[np.ndarray]:
        """Generate embeddings for code segments"""
        
        embeddings = []
        
        # Process functions
        for func in file_data.get("functions", []):
            # Tokenize code
            inputs = self.tokenizer(
                func["code"], 
                return_tensors="pt", 
                truncation=True,
                max_length=512
            )
            
            # Generate embedding
            with torch.no_grad():
                outputs = self.model(**inputs)
                embedding = outputs.last_hidden_state.mean(dim=1).numpy()
                embeddings.append(embedding)
        
        return embeddings
    
    async def _cluster_patterns(
        self, 
        embeddings: List[np.ndarray], 
        metadata: List[Dict]
    ) -> List[Pattern]:
        """Cluster similar code patterns"""
        
        # Convert to numpy array
        X = np.vstack(embeddings)
        
        # Perform clustering
        clustering = DBSCAN(eps=0.3, min_samples=3).fit(X)
        
        # Group by cluster
        patterns = {}
        for idx, label in enumerate(clustering.labels_):
            if label == -1:  # Noise
                continue
                
            if label not in patterns:
                patterns[label] = Pattern(
                    id=f"pattern_{label}",
                    examples=[],
                    centroid=None
                )
            
            patterns[label].examples.append({
                "code": metadata[idx]["code"],
                "file": metadata[idx]["file"],
                "embedding": embeddings[idx]
            })
        
        # Calculate centroids
        for pattern in patterns.values():
            embeddings = [ex["embedding"] for ex in pattern.examples]
            pattern.centroid = np.mean(embeddings, axis=0)
        
        return list(patterns.values())
    
    async def _generate_templates(self, patterns: List[Pattern]) -> Dict[str, str]:
        """Generate code templates from patterns"""
        
        templates = {}
        
        for pattern in patterns:
            # Use Gemini to generate template
            prompt = self._build_template_prompt(pattern)
            
            response = await self.genai.models.generate_content_async(
                model="gemini-2.0-flash",
                contents=[prompt],
                generation_config={
                    "temperature": 0.2,
                    "max_output_tokens": 1024
                }
            )
            
            templates[pattern.id] = response.text
        
        return templates
```

### 4. Marketplace Service (Go)

```go
// services/marketplace/main.go
package main

import (
    "context"
    "fmt"
    "log"
    "net/http"
    "os"
    
    "cloud.google.com/go/spanner"
    "github.com/gin-gonic/gin"
    "github.com/stripe/stripe-go/v75"
    
    "ccl/marketplace/handlers"
    "ccl/marketplace/models"
    "ccl/marketplace/services"
)

func main() {
    // Initialize Stripe
    stripe.Key = os.Getenv("STRIPE_SECRET_KEY")
    
    // Initialize Spanner client
    ctx := context.Background()
    client, err := spanner.NewClient(ctx, "projects/ccl-platform-prod/instances/ccl-main/databases/marketplace")
    if err != nil {
        log.Fatalf("Failed to create Spanner client: %v", err)
    }
    defer client.Close()
    
    // Initialize services
    patternService := services.NewPatternService(client)
    paymentService := services.NewPaymentService()
    
    // Setup router
    router := gin.New()
    router.Use(gin.Logger())
    router.Use(gin.Recovery())
    
    // Setup routes
    api := router.Group("/api/v1")
    {
        // Pattern routes
        api.GET("/patterns", handlers.ListPatterns(patternService))
        api.GET("/patterns/:id", handlers.GetPattern(patternService))
        api.POST("/patterns", handlers.CreatePattern(patternService))
        api.PUT("/patterns/:id", handlers.UpdatePattern(patternService))
        
        // Purchase routes
        api.POST("/purchase", handlers.PurchasePattern(paymentService))
        api.GET("/purchases", handlers.ListPurchases(paymentService))
        
        // Revenue routes
        api.GET("/revenue", handlers.GetRevenue(paymentService))
        api.GET("/revenue/payouts", handlers.GetPayouts(paymentService))
    }
    
    // Start server
    port := os.Getenv("PORT")
    if port == "" {
        port = "8080"
    }
    
    log.Printf("Starting marketplace service on port %s", port)
    if err := router.Run(":" + port); err != nil {
        log.Fatalf("Failed to start server: %v", err)
    }
}

// services/marketplace/services/pattern_service.go
package services

import (
    "context"
    "fmt"
    
    "cloud.google.com/go/spanner"
    "google.golang.org/api/iterator"
    
    "ccl/marketplace/models"
)

type PatternService struct {
    client *spanner.Client
}

func NewPatternService(client *spanner.Client) *PatternService {
    return &PatternService{client: client}
}

func (s *PatternService) ListPatterns(ctx context.Context, filters models.PatternFilters) ([]*models.Pattern, error) {
    query := `
        SELECT 
            pattern_id,
            name,
            description,
            language,
            category,
            price_cents,
            author_id,
            rating,
            download_count,
            created_at
        FROM patterns
        WHERE 1=1
    `
    
    params := map[string]interface{}{}
    
    if filters.Language != "" {
        query += " AND language = @language"
        params["language"] = filters.Language
    }
    
    if filters.Category != "" {
        query += " AND category = @category"
        params["category"] = filters.Category
    }
    
    if filters.MinRating > 0 {
        query += " AND rating >= @minRating"
        params["minRating"] = filters.MinRating
    }
    
    query += " ORDER BY download_count DESC LIMIT 100"
    
    stmt := spanner.NewStatement(query)
    for k, v := range params {
        stmt.Params[k] = v
    }
    
    var patterns []*models.Pattern
    
    iter := s.client.Single().Query(ctx, stmt)
    defer iter.Stop()
    
    for {
        row, err := iter.Next()
        if err == iterator.Done {
            break
        }
        if err != nil {
            return nil, fmt.Errorf("failed to iterate patterns: %w", err)
        }
        
        var p models.Pattern
        if err := row.ToStruct(&p); err != nil {
            return nil, fmt.Errorf("failed to parse pattern: %w", err)
        }
        
        patterns = append(patterns, &p)
    }
    
    return patterns, nil
}

func (s *PatternService) CreatePattern(ctx context.Context, pattern *models.Pattern) error {
    _, err := s.client.Apply(ctx, []*spanner.Mutation{
        spanner.InsertStruct("patterns", pattern),
    })
    
    return err
}
```

---

## Language Parser Framework

### WebAssembly Plugin System

```rust
// packages/ccl-core/src/parser/plugin.rs
use wasmtime::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Serialize, Deserialize)]
pub struct ParseResult {
    pub ast: serde_json::Value,
    pub dependencies: Vec<String>,
    pub exports: Vec<String>,
    pub metrics: CodeMetrics,
}

#[derive(Serialize, Deserialize)]
pub struct CodeMetrics {
    pub lines: usize,
    pub complexity: f32,
    pub functions: usize,
    pub classes: usize,
}

pub struct ParserPlugin {
    engine: Engine,
    module: Module,
    store: Store<()>,
}

impl ParserPlugin {
    pub fn new(wasm_bytes: &[u8]) -> Result<Self> {
        let engine = Engine::default();
        let module = Module::new(&engine, wasm_bytes)?;
        let store = Store::new(&engine, ());
        
        Ok(Self { engine, module, store })
    }
    
    pub fn parse(&mut self, code: &str) -> Result<ParseResult> {
        let instance = Instance::new(&mut self.store, &self.module, &[])?;
        
        // Allocate memory for input
        let memory = instance
            .get_memory(&mut self.store, "memory")
            .ok_or_else(|| anyhow!("Memory export not found"))?;
            
        let alloc = instance
            .get_typed_func::<i32, i32>(&mut self.store, "alloc")?;
            
        let parse = instance
            .get_typed_func::<(i32, i32), i32>(&mut self.store, "parse")?;
            
        // Allocate and write code to WASM memory
        let code_bytes = code.as_bytes();
        let ptr = alloc.call(&mut self.store, code_bytes.len() as i32)?;
        memory.write(&mut self.store, ptr as usize, code_bytes)?;
        
        // Call parse function
        let result_ptr = parse.call(&mut self.store, (ptr, code_bytes.len() as i32))?;
        
        // Read result from memory
        let result_len = self.read_u32(&memory, result_ptr as usize)?;
        let mut result_bytes = vec![0u8; result_len as usize];
        memory.read(&self.store, (result_ptr + 4) as usize, &mut result_bytes)?;
        
        // Deserialize result
        let result: ParseResult = serde_json::from_slice(&result_bytes)?;
        
        Ok(result)
    }
}

// Example JavaScript parser plugin (compiled to WASM)
// plugins/javascript/src/index.js
import { parse } from '@babel/parser';
import traverse from '@babel/traverse';

export function parseJavaScript(codePtr, codeLen) {
    // Read code from WASM memory
    const code = readString(codePtr, codeLen);
    
    // Parse with Babel
    const ast = parse(code, {
        sourceType: 'module',
        plugins: ['jsx', 'typescript', 'decorators-legacy']
    });
    
    // Extract information
    const dependencies = [];
    const exports = [];
    let functionCount = 0;
    let classCount = 0;
    
    traverse(ast, {
        ImportDeclaration(path) {
            dependencies.push(path.node.source.value);
        },
        ExportNamedDeclaration(path) {
            if (path.node.declaration) {
                exports.push(path.node.declaration.id?.name);
            }
        },
        FunctionDeclaration() {
            functionCount++;
        },
        ClassDeclaration() {
            classCount++;
        }
    });
    
    // Calculate metrics
    const lines = code.split('\n').length;
    const complexity = calculateComplexity(ast);
    
    // Return result
    const result = {
        ast: ast,
        dependencies,
        exports,
        metrics: {
            lines,
            complexity,
            functions: functionCount,
            classes: classCount
        }
    };
    
    return allocateAndWriteString(JSON.stringify(result));
}
```

### Language Support Matrix

```yaml
Supported Languages (Day 1):
  - name: JavaScript
    parser: babel
    extensions: [.js, .jsx, .mjs]
    
  - name: TypeScript
    parser: typescript
    extensions: [.ts, .tsx]
    
  - name: Python
    parser: tree-sitter-python
    extensions: [.py]
    
  - name: Java
    parser: tree-sitter-java
    extensions: [.java]
    
  - name: Go
    parser: tree-sitter-go
    extensions: [.go]
    
  - name: Rust
    parser: tree-sitter-rust
    extensions: [.rs]
    
  - name: C++
    parser: tree-sitter-cpp
    extensions: [.cpp, .cc, .cxx, .hpp]
    
  - name: C#
    parser: tree-sitter-c-sharp
    extensions: [.cs]
    
  - name: Ruby
    parser: tree-sitter-ruby
    extensions: [.rb]
    
  - name: PHP
    parser: tree-sitter-php
    extensions: [.php]
    
  - name: Swift
    parser: tree-sitter-swift
    extensions: [.swift]
    
  - name: Kotlin
    parser: tree-sitter-kotlin
    extensions: [.kt, .kts]
    
  - name: Scala
    parser: tree-sitter-scala
    extensions: [.scala]
    
  - name: Dart
    parser: tree-sitter-dart
    extensions: [.dart]
    
  - name: Elixir
    parser: tree-sitter-elixir
    extensions: [.ex, .exs]
    
  - name: Haskell
    parser: tree-sitter-haskell
    extensions: [.hs]
    
  - name: Lua
    parser: tree-sitter-lua
    extensions: [.lua]
    
  - name: R
    parser: tree-sitter-r
    extensions: [.r, .R]
    
  - name: Julia
    parser: tree-sitter-julia
    extensions: [.jl]
    
  - name: Objective-C
    parser: tree-sitter-objc
    extensions: [.m, .mm]
    
  - name: SQL
    parser: tree-sitter-sql
    extensions: [.sql]
    
  - name: HTML
    parser: tree-sitter-html
    extensions: [.html, .htm]
    
  - name: CSS
    parser: tree-sitter-css
    extensions: [.css, .scss, .sass]
    
  - name: YAML
    parser: tree-sitter-yaml
    extensions: [.yml, .yaml]
    
  - name: JSON
    parser: native-json
    extensions: [.json]
```

---

## AI/ML Implementation

### Gemini Integration

```python
# packages/ccl-core/src/ai/gemini_integration.py
from google import genai
from google.genai.types import (
    GenerateContentConfig,
    SafetySetting,
    HarmCategory,
    HarmBlockThreshold
)
import asyncio
from typing import List, Dict, Optional
import json

class GeminiIntegration:
    def __init__(self, project: str, location: str):
        self.client = genai.Client(
            vertexai=True,
            project=project,
            location=location
        )
        
        # Configure safety settings
        self.safety_settings = [
            SafetySetting(
                category=HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                threshold=HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
            ),
            SafetySetting(
                category=HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                threshold=HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
            ),
            SafetySetting(
                category=HarmCategory.HARM_CATEGORY_HARASSMENT,
                threshold=HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
            ),
            SafetySetting(
                category=HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                threshold=HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
            )
        ]
    
    async def analyze_code_intent(
        self, 
        code: str, 
        context: Optional[Dict] = None
    ) -> Dict:
        """Analyze code intent and purpose using Gemini"""
        
        prompt = self._build_code_analysis_prompt(code, context)
        
        config = GenerateContentConfig(
            temperature=0.1,  # Low temperature for factual analysis
            top_p=0.95,
            top_k=40,
            max_output_tokens=1024,
            response_modalities=["TEXT"],
            response_mime_type="application/json"
        )
        
        response = await self.client.models.generate_content_async(
            model="gemini-2.0-flash",
            contents=[genai.Part.from_text(prompt)],
            generation_config=config,
            safety_settings=self.safety_settings
        )
        
        # Parse JSON response
        return json.loads(response.text)
    
    async def generate_documentation(
        self,
        code: str,
        analysis: Dict
    ) -> str:
        """Generate natural language documentation"""
        
        prompt = f"""Generate comprehensive documentation for this code:

Code:
```
{code}
```

Analysis:
{json.dumps(analysis, indent=2)}

Generate documentation that includes:
1. Purpose and overview
2. Parameters and return values
3. Usage examples
4. Important notes or warnings
5. Related patterns in the codebase

Format as markdown."""

        config = GenerateContentConfig(
            temperature=0.3,
            top_p=0.95,
            top_k=40,
            max_output_tokens=2048,
            response_modalities=["TEXT"]
        )
        
        response = await self.client.models.generate_content_async(
            model="gemini-2.0-flash",
            contents=[genai.Part.from_text(prompt)],
            generation_config=config,
            safety_settings=self.safety_settings
        )
        
        return response.text
    
    async def explain_architecture(
        self,
        components: List[Dict],
        relationships: List[Dict]
    ) -> str:
        """Explain system architecture in natural language"""
        
        prompt = f"""Explain this software architecture in clear, accessible language:

Components:
{json.dumps(components, indent=2)}

Relationships:
{json.dumps(relationships, indent=2)}

Provide:
1. High-level overview
2. Key architectural patterns
3. Data flow explanation
4. Scalability considerations
5. Potential improvements

Target audience: Senior developers who are new to this codebase."""

        response = await self.client.models.generate_content_async(
            model="gemini-2.0-flash",
            contents=[genai.Part.from_text(prompt)],
            generation_config=GenerateContentConfig(
                temperature=0.4,
                max_output_tokens=2048
            ),
            safety_settings=self.safety_settings
        )
        
        return response.text
    
    async def suggest_improvements(
        self,
        code: str,
        patterns: List[Dict],
        metrics: Dict
    ) -> List[Dict]:
        """Suggest code improvements based on patterns and metrics"""
        
        prompt = f"""Analyze this code and suggest improvements:

Code:
```
{code}
```

Detected Patterns:
{json.dumps(patterns, indent=2)}

Code Metrics:
{json.dumps(metrics, indent=2)}

Provide specific, actionable improvements in JSON format:
{{
    "improvements": [
        {{
            "type": "performance|security|maintainability|pattern",
            "description": "Clear description of the improvement",
            "impact": "high|medium|low",
            "effort": "high|medium|low",
            "example": "Code example if applicable"
        }}
    ]
}}"""

        response = await self.client.models.generate_content_async(
            model="gemini-2.0-flash",
            contents=[genai.Part.from_text(prompt)],
            generation_config=GenerateContentConfig(
                temperature=0.2,
                response_mime_type="application/json"
            ),
            safety_settings=self.safety_settings
        )
        
        result = json.loads(response.text)
        return result["improvements"]
```

### Custom Model Training

```python
# packages/ccl-core/src/ml/pattern_recognition.py
import tensorflow as tf
from tensorflow.keras import layers, models
import numpy as np
from google.cloud import aiplatform
from google.cloud import storage
import json

class PatternRecognitionModel:
    def __init__(self):
        self.model = self._build_model()
        self.storage_client = storage.Client()
        
    def _build_model(self):
        """Build neural network for pattern recognition"""
        
        model = models.Sequential([
            # Input layer for code embeddings
            layers.Input(shape=(768,)),  # CodeBERT embedding size
            
            # Hidden layers with dropout
            layers.Dense(512, activation='relu'),
            layers.Dropout(0.3),
            layers.Dense(256, activation='relu'),
            layers.Dropout(0.3),
            layers.Dense(128, activation='relu'),
            layers.Dropout(0.2),
            
            # Output layer for pattern classification
            layers.Dense(50, activation='softmax')  # 50 pattern types
        ])
        
        model.compile(
            optimizer='adam',
            loss='categorical_crossentropy',
            metrics=['accuracy', 'top_k_categorical_accuracy']
        )
        
        return model
    
    def prepare_training_data(self, analyzed_repos: List[str]) -> tf.data.Dataset:
        """Prepare training data from analyzed repositories"""
        
        embeddings = []
        labels = []
        
        for repo_id in analyzed_repos:
            # Load analyzed data from Cloud Storage
            blob = self.storage_client.bucket('ccl-analysis-data').blob(
                f'embeddings/{repo_id}.npy'
            )
            repo_embeddings = np.load(blob.open('rb'))
            
            # Load pattern labels
            labels_blob = self.storage_client.bucket('ccl-analysis-data').blob(
                f'labels/{repo_id}.json'
            )
            repo_labels = json.loads(labels_blob.download_as_text())
            
            embeddings.extend(repo_embeddings)
            labels.extend(repo_labels)
        
        # Convert to TensorFlow dataset
        dataset = tf.data.Dataset.from_tensor_slices((
            np.array(embeddings),
            tf.keras.utils.to_categorical(labels, num_classes=50)
        ))
        
        # Shuffle and batch
        dataset = dataset.shuffle(10000).batch(64).prefetch(tf.data.AUTOTUNE)
        
        return dataset
    
    def train_on_vertex_ai(self, dataset_path: str):
        """Train model using Vertex AI"""
        
        # Initialize Vertex AI
        aiplatform.init(
            project="ccl-platform-prod",
            location="us-central1",
            staging_bucket="gs://ccl-ml-staging"
        )
        
        # Create custom training job
        job = aiplatform.CustomTrainingJob(
            display_name="ccl-pattern-recognition-training",
            script_path="training_script.py",
            container_uri="gcr.io/cloud-aiplatform/training/tf-gpu.2-11:latest",
            requirements=["tensorflow==2.11", "numpy", "google-cloud-storage"],
            model_serving_container_image_uri="gcr.io/cloud-aiplatform/prediction/tf2-gpu.2-11:latest"
        )
        
        # Define machine specs
        machine_spec = {
            "machine_type": "n1-standard-8",
            "accelerator_type": "NVIDIA_TESLA_V100",
            "accelerator_count": 1
        }
        
        # Run training
        model = job.run(
            dataset=dataset_path,
            model_display_name="ccl-pattern-model",
            args=[
                "--epochs", "50",
                "--batch_size", "64",
                "--learning_rate", "0.001"
            ],
            replica_count=1,
            machine_type=machine_spec["machine_type"],
            accelerator_type=machine_spec["accelerator_type"],
            accelerator_count=machine_spec["accelerator_count"]
        )
        
        # Deploy model
        endpoint = model.deploy(
            deployed_model_display_name="ccl-pattern-endpoint",
            machine_type="n1-standard-4",
            min_replica_count=1,
            max_replica_count=10,
            accelerator_type="NVIDIA_TESLA_T4",
            accelerator_count=1
        )
        
        return endpoint
```

### Embedding Generation

```python
# packages/ccl-core/src/ml/embeddings.py
from sentence_transformers import SentenceTransformer
import numpy as np
from typing import List, Dict
import asyncio
from concurrent.futures import ThreadPoolExecutor

class CodeEmbeddingGenerator:
    def __init__(self):
        # Use CodeBERT for code understanding
        self.model = SentenceTransformer('microsoft/codebert-base')
        self.executor = ThreadPoolExecutor(max_workers=4)
        
    async def generate_embeddings(
        self, 
        code_snippets: List[str]
    ) -> np.ndarray:
        """Generate embeddings for code snippets"""
        
        loop = asyncio.get_event_loop()
        
        # Run embedding generation in thread pool
        embeddings = await loop.run_in_executor(
            self.executor,
            self.model.encode,
            code_snippets,
            True  # normalize_embeddings
        )
        
        return embeddings
    
    async def generate_file_embedding(
        self,
        file_content: str,
        chunk_size: int = 512
    ) -> Dict:
        """Generate embeddings for entire file with chunking"""
        
        # Split into chunks
        lines = file_content.split('\n')
        chunks = []
        current_chunk = []
        current_size = 0
        
        for line in lines:
            if current_size + len(line) > chunk_size and current_chunk:
                chunks.append('\n'.join(current_chunk))
                current_chunk = [line]
                current_size = len(line)
            else:
                current_chunk.append(line)
                current_size += len(line)
        
        if current_chunk:
            chunks.append('\n'.join(current_chunk))
        
        # Generate embeddings for chunks
        chunk_embeddings = await self.generate_embeddings(chunks)
        
        # Aggregate embeddings
        file_embedding = np.mean(chunk_embeddings, axis=0)
        
        return {
            "file_embedding": file_embedding,
            "chunk_embeddings": chunk_embeddings,
            "chunks": chunks
        }
    
    async def build_similarity_index(
        self,
        embeddings: Dict[str, np.ndarray]
    ):
        """Build similarity index using Vertex AI Matching Engine"""
        
        from google.cloud import aiplatform
        
        # Initialize Vertex AI
        aiplatform.init(
            project="ccl-platform-prod",
            location="us-central1"
        )
        
        # Create index
        index = aiplatform.MatchingEngineIndex.create(
            display_name="ccl-code-similarity-index",
            dimensions=768,  # CodeBERT embedding dimensions
            approximate_neighbors_count=100,
            distance_measure_type="DOT_PRODUCT_DISTANCE",
            algorithm_config={
                "tree_ah_config": {
                    "leaf_node_embedding_count": 1000,
                    "leaf_nodes_to_search_percent": 10
                }
            }
        )
        
        # Create index endpoint
        index_endpoint = aiplatform.MatchingEngineIndexEndpoint.create(
            display_name="ccl-code-similarity-endpoint",
            public_endpoint_enabled=True
        )
        
        # Deploy index
        index_endpoint.deploy_index(
            index=index,
            deployed_index_id="ccl-code-index-deployment",
            machine_type="n1-standard-16",
            min_replica_count=2,
            max_replica_count=10
        )
        
        return index_endpoint
```

---

## Database Schemas & Models

### Spanner Schema

```sql
-- Complete Spanner schema for CCL
CREATE DATABASE ccl_main;

-- Users and authentication
CREATE TABLE users (
    user_id STRING(36) NOT NULL,
    email STRING(255) NOT NULL,
    name STRING(255),
    avatar_url STRING(1024),
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    subscription_tier STRING(50) NOT NULL DEFAULT 'free',
    subscription_expires_at TIMESTAMP,
    organization_id STRING(36),
    settings JSON,
    metadata JSON,
) PRIMARY KEY (user_id);

CREATE UNIQUE INDEX idx_users_email ON users(email);

-- Organizations
CREATE TABLE organizations (
    organization_id STRING(36) NOT NULL,
    name STRING(255) NOT NULL,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    subscription_tier STRING(50) NOT NULL DEFAULT 'enterprise',
    settings JSON,
    metadata JSON,
) PRIMARY KEY (organization_id);

-- Repositories
CREATE TABLE repositories (
    repository_id STRING(36) NOT NULL,
    user_id STRING(36) NOT NULL,
    organization_id STRING(36),
    name STRING(255) NOT NULL,
    url STRING(1024),
    provider STRING(50), -- github, gitlab, bitbucket, local
    last_analysis_at TIMESTAMP,
    last_commit_sha STRING(100),
    total_files INT64,
    total_lines INT64,
    primary_language STRING(50),
    languages JSON, -- {"javascript": 45.2, "python": 30.1, ...}
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    metadata JSON,
    FOREIGN KEY (user_id) REFERENCES users (user_id),
    FOREIGN KEY (organization_id) REFERENCES organizations (organization_id),
) PRIMARY KEY (repository_id);

CREATE INDEX idx_repos_user ON repositories(user_id);
CREATE INDEX idx_repos_org ON repositories(organization_id);

-- Analysis results
CREATE TABLE analyses (
    analysis_id STRING(36) NOT NULL,
    repository_id STRING(36) NOT NULL,
    started_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    completed_at TIMESTAMP,
    status STRING(50) NOT NULL, -- pending, running, completed, failed
    commit_sha STRING(100),
    files_analyzed INT64,
    patterns_detected INT64,
    duration_ms INT64,
    error_message STRING(MAX),
    results JSON, -- Detailed analysis results
    FOREIGN KEY (repository_id) REFERENCES repositories (repository_id),
) PRIMARY KEY (analysis_id),
INTERLEAVE IN PARENT repositories ON DELETE CASCADE;

-- Detected patterns
CREATE TABLE patterns (
    pattern_id STRING(36) NOT NULL,
    repository_id STRING(36) NOT NULL,
    analysis_id STRING(36) NOT NULL,
    pattern_type STRING(100) NOT NULL,
    pattern_name STRING(255),
    confidence FLOAT64 NOT NULL,
    occurrences INT64 NOT NULL,
    files JSON, -- List of files where pattern appears
    template TEXT,
    examples JSON,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    metadata JSON,
    FOREIGN KEY (repository_id) REFERENCES repositories (repository_id),
    FOREIGN KEY (analysis_id) REFERENCES analyses (analysis_id),
) PRIMARY KEY (pattern_id),
INTERLEAVE IN PARENT repositories ON DELETE CASCADE;

CREATE INDEX idx_patterns_type ON patterns(pattern_type);
CREATE INDEX idx_patterns_confidence ON patterns(confidence);

-- File analysis
CREATE TABLE file_analyses (
    file_id STRING(36) NOT NULL,
    repository_id STRING(36) NOT NULL,
    analysis_id STRING(36) NOT NULL,
    file_path STRING(1024) NOT NULL,
    language STRING(50),
    lines_of_code INT64,
    complexity_score FLOAT64,
    ast_hash STRING(64), -- For change detection
    dependencies JSON,
    exports JSON,
    functions JSON,
    classes JSON,
    patterns JSON,
    embeddings_id STRING(36), -- Reference to stored embeddings
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    FOREIGN KEY (repository_id) REFERENCES repositories (repository_id),
    FOREIGN KEY (analysis_id) REFERENCES analyses (analysis_id),
) PRIMARY KEY (file_id),
INTERLEAVE IN PARENT repositories ON DELETE CASCADE;

CREATE INDEX idx_files_path ON file_analyses(repository_id, file_path);

-- Conversations
CREATE TABLE conversations (
    conversation_id STRING(36) NOT NULL,
    user_id STRING(36) NOT NULL,
    repository_id STRING(36),
    started_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    last_message_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    message_count INT64 NOT NULL DEFAULT 0,
    context JSON,
    metadata JSON,
    FOREIGN KEY (user_id) REFERENCES users (user_id),
    FOREIGN KEY (repository_id) REFERENCES repositories (repository_id),
) PRIMARY KEY (conversation_id);

CREATE INDEX idx_conversations_user ON conversations(user_id);

-- Messages
CREATE TABLE messages (
    message_id STRING(36) NOT NULL,
    conversation_id STRING(36) NOT NULL,
    role STRING(50) NOT NULL, -- user, assistant
    content TEXT NOT NULL,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    tokens_used INT64,
    confidence_score FLOAT64,
    sources JSON, -- Referenced files/patterns
    metadata JSON,
    FOREIGN KEY (conversation_id) REFERENCES conversations (conversation_id),
) PRIMARY KEY (message_id),
INTERLEAVE IN PARENT conversations ON DELETE CASCADE;

-- Pattern marketplace
CREATE TABLE marketplace_patterns (
    pattern_id STRING(36) NOT NULL,
    author_id STRING(36) NOT NULL,
    name STRING(255) NOT NULL,
    description TEXT,
    category STRING(100),
    language STRING(50),
    price_cents INT64 NOT NULL DEFAULT 0,
    currency STRING(3) NOT NULL DEFAULT 'USD',
    template TEXT NOT NULL,
    documentation TEXT,
    examples JSON,
    tags ARRAY<STRING(50)>,
    version STRING(20),
    downloads INT64 NOT NULL DEFAULT 0,
    rating FLOAT64,
    rating_count INT64 NOT NULL DEFAULT 0,
    status STRING(50) NOT NULL DEFAULT 'draft', -- draft, published, deprecated
    published_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    metadata JSON,
    FOREIGN KEY (author_id) REFERENCES users (user_id),
) PRIMARY KEY (pattern_id);

CREATE INDEX idx_marketplace_category ON marketplace_patterns(category);
CREATE INDEX idx_marketplace_language ON marketplace_patterns(language);
CREATE INDEX idx_marketplace_status ON marketplace_patterns(status);

-- Pattern purchases
CREATE TABLE pattern_purchases (
    purchase_id STRING(36) NOT NULL,
    pattern_id STRING(36) NOT NULL,
    user_id STRING(36) NOT NULL,
    price_cents INT64 NOT NULL,
    currency STRING(3) NOT NULL,
    stripe_payment_id STRING(255),
    purchased_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    FOREIGN KEY (pattern_id) REFERENCES marketplace_patterns (pattern_id),
    FOREIGN KEY (user_id) REFERENCES users (user_id),
) PRIMARY KEY (purchase_id);

CREATE INDEX idx_purchases_user ON pattern_purchases(user_id);
CREATE INDEX idx_purchases_pattern ON pattern_purchases(pattern_id);

-- API keys
CREATE TABLE api_keys (
    key_id STRING(36) NOT NULL,
    user_id STRING(36) NOT NULL,
    organization_id STRING(36),
    key_hash STRING(64) NOT NULL, -- SHA256 of the actual key
    name STRING(255),
    scopes ARRAY<STRING(50)>,
    expires_at TIMESTAMP,
    last_used_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    revoked_at TIMESTAMP,
    metadata JSON,
    FOREIGN KEY (user_id) REFERENCES users (user_id),
    FOREIGN KEY (organization_id) REFERENCES organizations (organization_id),
) PRIMARY KEY (key_id);

CREATE INDEX idx_api_keys_hash ON api_keys(key_hash);
CREATE INDEX idx_api_keys_user ON api_keys(user_id);

-- Audit logs
CREATE TABLE audit_logs (
    log_id STRING(36) NOT NULL,
    user_id STRING(36),
    organization_id STRING(36),
    action STRING(100) NOT NULL,
    resource_type STRING(50),
    resource_id STRING(36),
    ip_address STRING(45),
    user_agent STRING(1024),
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    metadata JSON,
) PRIMARY KEY (log_id);

CREATE INDEX idx_audit_user ON audit_logs(user_id);
CREATE INDEX idx_audit_org ON audit_logs(organization_id);
CREATE INDEX idx_audit_action ON audit_logs(action);
CREATE INDEX idx_audit_time ON audit_logs(created_at DESC);
```

### BigQuery Analytics Schema

```sql
-- Create datasets
CREATE SCHEMA IF NOT EXISTS ccl_analytics
OPTIONS(
  location="us-central1",
  default_table_expiration_ms=7776000000  -- 90 days
);

CREATE SCHEMA IF NOT EXISTS ccl_ml
OPTIONS(
  location="us-central1"
);

-- Analysis events table
CREATE OR REPLACE TABLE ccl_analytics.analysis_events (
  event_id STRING NOT NULL,
  timestamp TIMESTAMP NOT NULL,
  user_id STRING,
  organization_id STRING,
  repository_id STRING NOT NULL,
  event_type STRING NOT NULL, -- started, completed, failed
  duration_ms INT64,
  files_analyzed INT64,
  patterns_detected INT64,
  lines_of_code INT64,
  languages ARRAY<STRUCT<
    language STRING,
    percentage FLOAT64
  >>,
  error_type STRING,
  error_message STRING,
  metadata JSON
)
PARTITION BY DATE(timestamp)
CLUSTER BY user_id, repository_id, event_type
OPTIONS(
  description="Repository analysis events",
  labels=[("team", "engineering"), ("tier", "analytics")]
);

-- Query analytics table
CREATE OR REPLACE TABLE ccl_analytics.query_logs (
  query_id STRING NOT NULL,
  timestamp TIMESTAMP NOT NULL,
  user_id STRING NOT NULL,
  organization_id STRING,
  repository_id STRING,
  conversation_id STRING,
  query_text STRING,
  query_type STRING, -- architecture, pattern, code, general
  response_time_ms INT64,
  tokens_used INT64,
  model_used STRING,
  confidence_score FLOAT64,
  sources_count INT64,
  satisfaction_score INT64, -- 1-5 rating
  follow_up BOOLEAN,
  metadata JSON
)
PARTITION BY DATE(timestamp)
CLUSTER BY user_id, conversation_id
OPTIONS(
  description="Natural language query logs"
);

-- Pattern usage analytics
CREATE OR REPLACE TABLE ccl_analytics.pattern_events (
  event_id STRING NOT NULL,
  timestamp TIMESTAMP NOT NULL,
  user_id STRING NOT NULL,
  pattern_id STRING NOT NULL,
  repository_id STRING,
  action STRING NOT NULL, -- detected, viewed, copied, implemented, purchased
  confidence_score FLOAT64,
  context JSON
)
PARTITION BY DATE(timestamp)
CLUSTER BY pattern_id, action;

-- User behavior analytics
CREATE OR REPLACE TABLE ccl_analytics.user_sessions (
  session_id STRING NOT NULL,
  user_id STRING NOT NULL,
  started_at TIMESTAMP NOT NULL,
  ended_at TIMESTAMP,
  duration_seconds INT64,
  pages_viewed INT64,
  actions_performed INT64,
  features_used ARRAY<STRING>,
  device_type STRING,
  browser STRING,
  ip_country STRING,
  referrer STRING,
  metadata JSON
)
PARTITION BY DATE(started_at)
CLUSTER BY user_id;

-- Revenue analytics
CREATE OR REPLACE TABLE ccl_analytics.revenue_events (
  event_id STRING NOT NULL,
  timestamp TIMESTAMP NOT NULL,
  user_id STRING NOT NULL,
  event_type STRING NOT NULL, -- subscription, purchase, renewal, churn
  amount_cents INT64,
  currency STRING,
  product_type STRING, -- subscription_tier, pattern, api_usage
  product_id STRING,
  stripe_id STRING,
  metadata JSON
)
PARTITION BY DATE(timestamp)
CLUSTER BY user_id, event_type;

-- ML training data
CREATE OR REPLACE TABLE ccl_ml.pattern_training_data (
  example_id STRING NOT NULL,
  repository_id STRING NOT NULL,
  file_path STRING,
  code_snippet STRING,
  pattern_type STRING NOT NULL,
  pattern_confidence FLOAT64,
  embedding ARRAY<FLOAT64>,
  features STRUCT<
    lines_of_code INT64,
    complexity_score FLOAT64,
    dependencies_count INT64,
    function_count INT64,
    class_count INT64
  >,
  created_at TIMESTAMP
)
PARTITION BY DATE(created_at)
CLUSTER BY pattern_type;

-- Create views for common queries
CREATE OR REPLACE VIEW ccl_analytics.daily_active_users AS
SELECT
  DATE(timestamp) as date,
  COUNT(DISTINCT user_id) as dau,
  COUNT(DISTINCT organization_id) as active_orgs,
  SUM(tokens_used) as total_tokens,
  AVG(response_time_ms) as avg_response_time,
  AVG(satisfaction_score) as avg_satisfaction
FROM ccl_analytics.query_logs
WHERE timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
GROUP BY date
ORDER BY date DESC;

CREATE OR REPLACE VIEW ccl_analytics.pattern_popularity AS
SELECT
  p.pattern_type,
  p.pattern_name,
  COUNT(DISTINCT e.repository_id) as repos_using,
  SUM(p.occurrences) as total_occurrences,
  AVG(p.confidence) as avg_confidence,
  COUNT(DISTINCT pe.user_id) as users_interacted
FROM ccl_analytics.patterns p
LEFT JOIN ccl_analytics.pattern_events pe 
  ON p.pattern_id = pe.pattern_id
GROUP BY p.pattern_type, p.pattern_name
ORDER BY repos_using DESC;
```

### Firestore Collections

```typescript
// Firestore collection schemas (TypeScript interfaces)

// Real-time collaboration sessions
interface Session {
  sessionId: string;
  repositoryId: string;
  createdAt: FirebaseFirestore.Timestamp;
  participants: {
    [userId: string]: {
      name: string;
      email: string;
      avatarUrl?: string;
      cursor?: {
        file: string;
        line: number;
        column: number;
      };
      status: 'active' | 'idle' | 'offline';
      lastActivity: FirebaseFirestore.Timestamp;
    }
  };
  sharedContext: {
    currentFile?: string;
    highlightedCode?: {
      file: string;
      startLine: number;
      endLine: number;
    };
    activePattern?: string;
    notes?: string;
  };
}

// Conversation state for real-time updates
interface ConversationState {
  conversationId: string;
  userId: string;
  repositoryId?: string;
  messages: Array<{
    messageId: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: FirebaseFirestore.Timestamp;
    sources?: string[];
    codeSnippets?: Array<{
      file: string;
      startLine: number;
      endLine: number;
      code: string;
    }>;
  }>;
  context: {
    currentFocus?: string;
    relevantFiles?: string[];
    activePatterns?: string[];
  };
  isTyping?: boolean;
  lastUpdated: FirebaseFirestore.Timestamp;
}

// User preferences (synced across devices)
interface UserPreferences {
  userId: string;
  theme: 'light' | 'dark' | 'system';
  editorSettings: {
    fontSize: number;
    fontFamily: string;
    tabSize: number;
    wordWrap: boolean;
  };
  notificationSettings: {
    email: boolean;
    browser: boolean;
    analysisComplete: boolean;
    marketplaceUpdates: boolean;
  };
  favoritePatterns: string[];
  recentRepositories: Array<{
    repositoryId: string;
    name: string;
    lastAccessed: FirebaseFirestore.Timestamp;
  }>;
  shortcuts: {
    [action: string]: string; // keyboard shortcuts
  };
}

// Pattern drafts (auto-saved)
interface PatternDraft {
  draftId: string;
  userId: string;
  name: string;
  description: string;
  template: string;
  examples: string[];
  category: string;
  language: string;
  tags: string[];
  lastSaved: FirebaseFirestore.Timestamp;
  isPublished: boolean;
}

// Real-time notifications
interface Notification {
  notificationId: string;
  userId: string;
  type: 'analysis_complete' | 'pattern_purchased' | 'mention' | 'system';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: FirebaseFirestore.Timestamp;
  expiresAt?: FirebaseFirestore.Timestamp;
}
```

---

## API Implementation

### REST API with Apigee

```yaml
# infrastructure/apigee/api-spec.yaml
openapi: 3.0.0
info:
  title: CCL API
  version: 1.0.0
  description: Codebase Context Layer API

servers:
  - url: https://api.ccl.dev/v1
    description: Production API

security:
  - OAuth2: []
  - ApiKey: []

paths:
  /analyze:
    post:
      summary: Analyze a repository
      operationId: analyzeRepository
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - repository_url
              properties:
                repository_url:
                  type: string
                  format: uri
                branch:
                  type: string
                  default: main
                languages:
                  type: array
                  items:
                    type: string
                incremental:
                  type: boolean
                  default: false
      responses:
        '202':
          description: Analysis started
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalysisResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/RateLimited'

  /analysis/{analysisId}:
    get:
      summary: Get analysis status and results
      operationId: getAnalysis
      parameters:
        - name: analysisId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Analysis details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Analysis'
        '404':
          $ref: '#/components/responses/NotFound'

  /query:
    post:
      summary: Query codebase with natural language
      operationId: queryCodebase
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - query
              properties:
                query:
                  type: string
                  maxLength: 1000
                repository_id:
                  type: string
                  format: uuid
                conversation_id:
                  type: string
                  format: uuid
                context:
                  type: object
      responses:
        '200':
          description: Query response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QueryResponse'
            text/event-stream:
              schema:
                type: string
                description: Server-sent events for streaming response

  /patterns:
    get:
      summary: List detected patterns
      operationId: listPatterns
      parameters:
        - name: repository_id
          in: query
          schema:
            type: string
            format: uuid
        - name: type
          in: query
          schema:
            type: string
        - name: language
          in: query
          schema:
            type: string
        - name: min_confidence
          in: query
          schema:
            type: number
            minimum: 0
            maximum: 1
        - $ref: '#/components/parameters/pagination'
      responses:
        '200':
          description: Pattern list
          content:
            application/json:
              schema:
                type: object
                properties:
                  patterns:
                    type: array
                    items:
                      $ref: '#/components/schemas/Pattern'
                  pagination:
                    $ref: '#/components/schemas/Pagination'

  /marketplace/patterns:
    get:
      summary: Browse marketplace patterns
      operationId: browseMarketplace
      parameters:
        - name: category
          in: query
          schema:
            type: string
        - name: language
          in: query
          schema:
            type: string
        - name: sort
          in: query
          schema:
            type: string
            enum: [popular, recent, rating, price]
        - $ref: '#/components/parameters/pagination'
      responses:
        '200':
          description: Marketplace patterns
          content:
            application/json:
              schema:
                type: object
                properties:
                  patterns:
                    type: array
                    items:
                      $ref: '#/components/schemas/MarketplacePattern'
                  pagination:
                    $ref: '#/components/schemas/Pagination'

components:
  securitySchemes:
    OAuth2:
      type: oauth2
      flows:
        authorizationCode:
          authorizationUrl: https://auth.ccl.dev/oauth/authorize
          tokenUrl: https://auth.ccl.dev/oauth/token
          scopes:
            read:analysis: Read analysis results
            write:analysis: Create analyses
            read:patterns: Read patterns
            write:patterns: Create patterns
            admin: Full access
    
    ApiKey:
      type: apiKey
      in: header
      name: X-API-Key

  schemas:
    AnalysisResponse:
      type: object
      properties:
        analysis_id:
          type: string
          format: uuid
        status:
          type: string
          enum: [pending, running, completed, failed]
        estimated_time_seconds:
          type: integer
        webhook_url:
          type: string
          format: uri

    Analysis:
      type: object
      properties:
        analysis_id:
          type: string
          format: uuid
        repository_id:
          type: string
          format: uuid
        status:
          type: string
          enum: [pending, running, completed, failed]
        started_at:
          type: string
          format: date-time
        completed_at:
          type: string
          format: date-time
        statistics:
          type: object
          properties:
            files_analyzed:
              type: integer
            total_lines:
              type: integer
            patterns_detected:
              type: integer
            languages:
              type: object
              additionalProperties:
                type: number
        results:
          type: object
          properties:
            architecture:
              $ref: '#/components/schemas/Architecture'
            patterns:
              type: array
              items:
                $ref: '#/components/schemas/Pattern'
            metrics:
              $ref: '#/components/schemas/CodeMetrics'

    QueryResponse:
      type: object
      properties:
        answer:
          type: string
        confidence:
          type: number
          minimum: 0
          maximum: 1
        sources:
          type: array
          items:
            type: object
            properties:
              file:
                type: string
              line_start:
                type: integer
              line_end:
                type: integer
              relevance:
                type: number
        code_snippets:
          type: array
          items:
            type: object
            properties:
              file:
                type: string
              code:
                type: string
              language:
                type: string
        suggested_questions:
          type: array
          items:
            type: string
        conversation_id:
          type: string
          format: uuid
```

### GraphQL Implementation

```typescript
// services/gateway/src/graphql/schema.ts
import { gql } from 'apollo-server-express';

export const typeDefs = gql`
  scalar DateTime
  scalar JSON

  type Query {
    # User queries
    me: User
    user(id: ID!): User
    
    # Repository queries
    repository(id: ID!): Repository
    repositories(
      userId: ID
      organizationId: ID
      limit: Int = 20
      offset: Int = 0
    ): RepositoryConnection!
    
    # Pattern queries
    pattern(id: ID!): Pattern
    patterns(
      repositoryId: ID
      type: PatternType
      language: String
      minConfidence: Float
      limit: Int = 20
      offset: Int = 0
    ): PatternConnection!
    
    # Analysis queries
    analysis(id: ID!): Analysis
    analyses(
      repositoryId: ID!
      limit: Int = 10
      offset: Int = 0
    ): AnalysisConnection!
    
    # Search
    searchCode(
      query: String!
      repositoryId: ID!
      limit: Int = 10
    ): [CodeSearchResult!]!
    
    searchPatterns(
      query: String!
      language: String
      limit: Int = 20
    ): [Pattern!]!
    
    # Marketplace
    marketplacePatterns(
      category: String
      language: String
      sort: MarketplaceSort
      limit: Int = 20
      offset: Int = 0
    ): MarketplacePatternConnection!
  }

  type Mutation {
    # Analysis mutations
    analyzeRepository(input: AnalyzeRepositoryInput!): Analysis!
    cancelAnalysis(analysisId: ID!): Analysis!
    
    # Pattern mutations
    createPattern(input: CreatePatternInput!): Pattern!
    updatePattern(id: ID!, input: UpdatePatternInput!): Pattern!
    deletePattern(id: ID!): Boolean!
    
    publishPattern(patternId: ID!): MarketplacePattern!
    purchasePattern(patternId: ID!): Purchase!
    
    # Query mutations
    askQuestion(input: AskQuestionInput!): QueryResponse!
    rateResponse(
      conversationId: ID!
      messageId: ID!
      rating: Int!
    ): Boolean!
    
    # User mutations
    updateProfile(input: UpdateProfileInput!): User!
    updatePreferences(preferences: JSON!): User!
  }

  type Subscription {
    # Analysis progress
    analysisProgress(analysisId: ID!): AnalysisProgress!
    
    # Real-time collaboration
    sessionUpdates(sessionId: ID!): SessionUpdate!
    
    # Conversation updates
    conversationUpdates(conversationId: ID!): ConversationUpdate!
    
    # Notifications
    notifications(userId: ID!): Notification!
  }

  # Core types
  type User {
    id: ID!
    email: String!
    name: String
    avatarUrl: String
    createdAt: DateTime!
    subscription: Subscription!
    organization: Organization
    repositories: [Repository!]!
    preferences: JSON
  }

  type Organization {
    id: ID!
    name: String!
    users: [User!]!
    repositories: [Repository!]!
    subscription: Subscription!
  }

  type Repository {
    id: ID!
    name: String!
    url: String
    provider: RepositoryProvider!
    owner: User!
    organization: Organization
    lastAnalysis: Analysis
    languages: JSON!
    statistics: RepositoryStatistics!
    patterns(
      type: PatternType
      minConfidence: Float
    ): [Pattern!]!
  }

  type Analysis {
    id: ID!
    repository: Repository!
    status: AnalysisStatus!
    startedAt: DateTime!
    completedAt: DateTime
    commit: String
    statistics: AnalysisStatistics!
    results: AnalysisResults
    error: String
  }

  type Pattern {
    id: ID!
    repository: Repository!
    type: PatternType!
    name: String!
    confidence: Float!
    occurrences: Int!
    files: [String!]!
    template: String
    examples: [PatternExample!]!
    similar: [Pattern!]!
  }

  type QueryResponse {
    answer: String!
    confidence: Float!
    sources: [Source!]!
    codeSnippets: [CodeSnippet!]!
    suggestedQuestions: [String!]!
    conversationId: ID!
  }

  # Enums
  enum RepositoryProvider {
    GITHUB
    GITLAB
    BITBUCKET
    LOCAL
  }

  enum AnalysisStatus {
    PENDING
    RUNNING
    COMPLETED
    FAILED
  }

  enum PatternType {
    ARCHITECTURAL
    DESIGN
    IDIOM
    ANTI_PATTERN
  }

  enum MarketplaceSort {
    POPULAR
    RECENT
    RATING
    PRICE_LOW
    PRICE_HIGH
  }

  # Input types
  input AnalyzeRepositoryInput {
    repositoryUrl: String!
    branch: String
    languages: [String!]
    incremental: Boolean
  }

  input CreatePatternInput {
    name: String!
    description: String
    template: String!
    category: String!
    language: String!
    examples: [String!]
    tags: [String!]
  }

  input AskQuestionInput {
    question: String!
    repositoryId: ID
    conversationId: ID
    context: JSON
  }
`;

// Resolvers implementation
export const resolvers = {
  Query: {
    me: async (_, __, { user, dataSources }) => {
      return dataSources.userAPI.getUser(user.id);
    },
    
    repository: async (_, { id }, { dataSources }) => {
      return dataSources.repositoryAPI.getRepository(id);
    },
    
    searchCode: async (_, { query, repositoryId, limit }, { dataSources }) => {
      return dataSources.searchAPI.searchCode(query, repositoryId, limit);
    },
  },
  
  Mutation: {
    analyzeRepository: async (_, { input }, { dataSources, pubsub }) => {
      const analysis = await dataSources.analysisAPI.startAnalysis(input);
      
      // Publish to subscription
      pubsub.publish('ANALYSIS_STARTED', {
        analysisProgress: {
          analysisId: analysis.id,
          status: 'STARTED',
          progress: 0,
        },
      });
      
      return analysis;
    },
    
    askQuestion: async (_, { input }, { dataSources, user }) => {
      return dataSources.queryAPI.processQuery({
        ...input,
        userId: user.id,
      });
    },
  },
  
  Subscription: {
    analysisProgress: {
      subscribe: (_, { analysisId }, { pubsub }) => {
        return pubsub.asyncIterator([`ANALYSIS_PROGRESS_${analysisId}`]);
      },
    },
  },
  
  // Field resolvers
  Repository: {
    patterns: async (repository, { type, minConfidence }, { dataSources }) => {
      return dataSources.patternAPI.getPatterns({
        repositoryId: repository.id,
        type,
        minConfidence,
      });
    },
  },
};
```

---

## Security Implementation

### Authentication & Authorization

```typescript
// services/auth/src/auth.ts
import { OAuth2Client } from 'google-auth-library';
import * as jwt from 'jsonwebtoken';
import { Firestore } from '@google-cloud/firestore';
import { SecretManagerServiceClient } from '@google-cloud/secret-manager';
import * as bcrypt from 'bcrypt';

export class AuthService {
  private oauth2Client: OAuth2Client;
  private firestore: Firestore;
  private secretManager: SecretManagerServiceClient;
  
  constructor() {
    this.oauth2Client = new OAuth2Client(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.REDIRECT_URL
    );
    this.firestore = new Firestore();
    this.secretManager = new SecretManagerServiceClient();
  }
  
  async authenticateWithGoogle(code: string): Promise<AuthResult> {
    // Exchange code for tokens
    const { tokens } = await this.oauth2Client.getToken(code);
    
    // Verify ID token
    const ticket = await this.oauth2Client.verifyIdToken({
      idToken: tokens.id_token!,
      audience: process.env.GOOGLE_CLIENT_ID,
    });
    
    const payload = ticket.getPayload()!;
    
    // Create or update user
    const user = await this.createOrUpdateUser({
      googleId: payload.sub,
      email: payload.email!,
      name: payload.name,
      picture: payload.picture,
    });
    
    // Generate CCL tokens
    const accessToken = await this.generateAccessToken(user);
    const refreshToken = await this.generateRefreshToken(user);
    
    return {
      user,
      accessToken,
      refreshToken,
    };
  }
  
  async generateAccessToken(user: User): Promise<string> {
    const secret = await this.getSecret('jwt-secret');
    
    return jwt.sign(
      {
        sub: user.id,
        email: user.email,
        role: user.role,
        permissions: user.permissions,
        organizationId: user.organizationId,
      },
      secret,
      {
        expiresIn: '1h',
        issuer: 'ccl.dev',
        audience: 'api.ccl.dev',
      }
    );
  }
  
  async validateApiKey(keyHash: string): Promise<ApiKeyValidation> {
    // Query Spanner for API key
    const query = `
      SELECT key_id, user_id, organization_id, scopes, expires_at
      FROM api_keys
      WHERE key_hash = @keyHash
      AND (expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP())
      AND revoked_at IS NULL
    `;
    
    const [rows] = await this.spanner.run({
      sql: query,
      params: { keyHash },
    });
    
    if (rows.length === 0) {
      throw new UnauthorizedError('Invalid API key');
    }
    
    const apiKey = rows[0];
    
    // Update last used timestamp
    await this.updateApiKeyLastUsed(apiKey.key_id);
    
    return {
      userId: apiKey.user_id,
      organizationId: apiKey.organization_id,
      scopes: apiKey.scopes,
    };
  }
  
  // Role-based access control
  async authorize(
    user: AuthenticatedUser,
    resource: string,
    action: string
  ): Promise<boolean> {
    // Check user permissions
    if (user.permissions.includes(`${resource}:${action}`)) {
      return true;
    }
    
    // Check role permissions
    const rolePermissions = await this.getRolePermissions(user.role);
    if (rolePermissions.includes(`${resource}:${action}`)) {
      return true;
    }
    
    // Check organization permissions
    if (user.organizationId) {
      const orgPermissions = await this.getOrganizationPermissions(
        user.organizationId,
        user.id
      );
      if (orgPermissions.includes(`${resource}:${action}`)) {
        return true;
      }
    }
    
    return false;
  }
}

// Middleware for Express
export function authenticate(optional = false) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const token = extractToken(req);
      
      if (!token && optional) {
        return next();
      }
      
      if (!token) {
        throw new UnauthorizedError('No authentication token provided');
      }
      
      const authService = new AuthService();
      const user = await authService.validateToken(token);
      
      req.user = user;
      next();
    } catch (error) {
      next(error);
    }
  };
}

export function authorize(resource: string, action: string) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new UnauthorizedError('Not authenticated');
      }
      
      const authService = new AuthService();
      const authorized = await authService.authorize(
        req.user,
        resource,
        action
      );
      
      if (!authorized) {
        throw new ForbiddenError(
          `Not authorized to ${action} ${resource}`
        );
      }
      
      next();
    } catch (error) {
      next(error);
    }
  };
}
```

### Encryption & Key Management

```python
# packages/ccl-core/src/security/encryption.py
from google.cloud import kms
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os

class EncryptionService:
    def __init__(self, project_id: str, location: str):
        self.kms_client = kms.KeyManagementServiceClient()
        self.project_id = project_id
        self.location = location
        self.key_ring_name = "ccl-encryption-keys"
        
    async def encrypt_sensitive_data(
        self, 
        data: str, 
        key_name: str = "data-encryption-key"
    ) -> str:
        """Encrypt sensitive data using Cloud KMS"""
        
        # Get KMS key path
        key_path = self.kms_client.crypto_key_path(
            self.project_id,
            self.location,
            self.key_ring_name,
            key_name
        )
        
        # Encrypt data
        response = self.kms_client.encrypt(
            request={
                "name": key_path,
                "plaintext": data.encode("utf-8"),
            }
        )
        
        # Return base64 encoded ciphertext
        return base64.b64encode(response.ciphertext).decode("utf-8")
    
    async def decrypt_sensitive_data(
        self,
        encrypted_data: str,
        key_name: str = "data-encryption-key"
    ) -> str:
        """Decrypt data encrypted with Cloud KMS"""
        
        # Get KMS key path
        key_path = self.kms_client.crypto_key_path(
            self.project_id,
            self.location,
            self.key_ring_name,
            key_name
        )
        
        # Decode from base64
        ciphertext = base64.b64decode(encrypted_data)
        
        # Decrypt data
        response = self.kms_client.decrypt(
            request={
                "name": key_path,
                "ciphertext": ciphertext,
            }
        )
        
        return response.plaintext.decode("utf-8")
    
    def generate_data_encryption_key(self) -> bytes:
        """Generate a data encryption key for field-level encryption"""
        
        # Generate random key
        return Fernet.generate_key()
    
    def encrypt_field(self, data: str, key: bytes) -> str:
        """Encrypt individual field using Fernet"""
        
        f = Fernet(key)
        return f.encrypt(data.encode()).decode()
    
    def decrypt_field(self, encrypted_data: str, key: bytes) -> str:
        """Decrypt field encrypted with Fernet"""
        
        f = Fernet(key)
        return f.decrypt(encrypted_data.encode()).decode()
    
    async def rotate_encryption_keys(self):
        """Rotate all encryption keys"""
        
        # Create new key version in Cloud KMS
        key_path = self.kms_client.crypto_key_path(
            self.project_id,
            self.location,
            self.key_ring_name,
            "data-encryption-key"
        )
        
        # Create new version
        new_version = self.kms_client.create_crypto_key_version(
            request={
                "parent": key_path,
                "crypto_key_version": {
                    "state": kms.CryptoKeyVersion.CryptoKeyVersionState.ENABLED
                }
            }
        )
        
        # Update primary version
        self.kms_client.update_crypto_key_primary_version(
            request={
                "name": key_path,
                "crypto_key_version_id": new_version.name.split("/")[-1]
            }
        )
        
        # Schedule re-encryption of existing data
        await self._schedule_data_reencryption()
```

### Security Headers & CORS

```typescript
// services/gateway/src/security/headers.ts
import helmet from 'helmet';
import cors from 'cors';
import { Express } from 'express';

export function configureSecurityHeaders(app: Express) {
  // Helmet for security headers
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'", "https://apis.google.com"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: [
          "'self'",
          "https://api.ccl.dev",
          "wss://api.ccl.dev",
          "https://*.googleapis.com"
        ],
        frameSrc: ["'none'"],
        objectSrc: ["'none'"],
        upgradeInsecureRequests: [],
      },
    },
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true,
    },
  }));
  
  // CORS configuration
  const corsOptions: cors.CorsOptions = {
    origin: (origin, callback) => {
      const allowedOrigins = [
        'https://ccl.dev',
        'https://app.ccl.dev',
        'https://marketplace.ccl.dev',
      ];
      
      // Allow requests with no origin (mobile apps, Postman)
      if (!origin) return callback(null, true);
      
      // Check if origin is allowed
      if (allowedOrigins.includes(origin)) {
        callback(null, true);
      } else if (process.env.NODE_ENV === 'development') {
        // Allow localhost in development
        if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
          callback(null, true);
        } else {
          callback(new Error('Not allowed by CORS'));
        }
      } else {
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-API-Key',
      'X-Request-ID',
    ],
    exposedHeaders: [
      'X-Request-ID',
      'X-RateLimit-Limit',
      'X-RateLimit-Remaining',
      'X-RateLimit-Reset',
    ],
    maxAge: 86400, // 24 hours
  };
  
  app.use(cors(corsOptions));
  
  // Additional security headers
  app.use((req, res, next) => {
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
    next();
  });
}
```

---

## Testing Strategy

### Unit Testing

```typescript
// tests/unit/pattern-detector.test.ts
import { describe, it, expect, beforeEach } from '@jest/globals';
import { PatternDetector } from '../../services/analysis-engine/src/patterns';
import { mockAST, mockCode } from '../fixtures/code-samples';

describe('PatternDetector', () => {
  let detector: PatternDetector;
  
  beforeEach(() => {
    detector = new PatternDetector();
  });
  
  describe('detectPatterns', () => {
    it('should detect Repository pattern', async () => {
      const patterns = await detector.detectPatterns(
        mockAST.repositoryPattern,
        'javascript'
      );
      
      expect(patterns).toContainEqual(
        expect.objectContaining({
          type: 'repository',
          confidence: expect.any(Number),
          occurrences: expect.any(Number),
        })
      );
    });
    
    it('should detect Singleton pattern', async () => {
      const patterns = await detector.detectPatterns(
        mockAST.singletonPattern,
        'javascript'
      );
      
      expect(patterns).toContainEqual(
        expect.objectContaining({
          type: 'singleton',
          confidence: expect.any(Number),
        })
      );
    });
    
    it('should handle malformed AST gracefully', async () => {
      const patterns = await detector.detectPatterns(
        { invalid: 'ast' },
        'javascript'
      );
      
      expect(patterns).toEqual([]);
    });
  });
  
  describe('generateTemplate', () => {
    it('should generate valid template from pattern examples', async () => {
      const examples = [
        mockCode.userRepository,
        mockCode.productRepository,
        mockCode.orderRepository,
      ];
      
      const template = await detector.generateTemplate(
        'repository',
        examples
      );
      
      expect(template).toContain('class ${EntityName}Repository');
      expect(template).toContain('findById');
      expect(template).toContain('findAll');
      expect(template).toContain('create');
      expect(template).toContain('update');
    });
  });
});
```

### Integration Testing

```python
# tests/integration/test_gemini_integration.py
import pytest
import asyncio
from unittest.mock import Mock, patch
from ccl.ai.gemini_integration import GeminiIntegration

@pytest.fixture
def gemini_client():
    return GeminiIntegration(
        project="test-project",
        location="us-central1"
    )

@pytest.mark.asyncio
async def test_analyze_code_intent(gemini_client):
    """Test code intent analysis with Gemini"""
    
    code = """
    class UserRepository:
        def __init__(self, db):
            self.db = db
            
        async def find_by_id(self, user_id):
            return await self.db.query(
                "SELECT * FROM users WHERE id = ?",
                user_id
            )
            
        async def create(self, user_data):
            return await self.db.execute(
                "INSERT INTO users (name, email) VALUES (?, ?)",
                user_data["name"],
                user_data["email"]
            )
    """
    
    with patch.object(gemini_client.client.models, 'generate_content_async') as mock_generate:
        mock_generate.return_value.text = '''
        {
            "purpose": "Data access layer for user management",
            "pattern": "Repository",
            "operations": ["create", "read"],
            "dependencies": ["database"],
            "security_concerns": ["SQL injection risk if not properly parameterized"]
        }
        '''
        
        result = await gemini_client.analyze_code_intent(code)
        
        assert result["pattern"] == "Repository"
        assert "create" in result["operations"]
        assert "security_concerns" in result

@pytest.mark.asyncio
async def test_generate_documentation(gemini_client):
    """Test documentation generation"""
    
    code = "def calculate_discount(price, percentage): return price * (1 - percentage / 100)"
    analysis = {
        "purpose": "Calculate discounted price",
        "parameters": ["price", "percentage"],
        "returns": "discounted price"
    }
    
    with patch.object(gemini_client.client.models, 'generate_content_async') as mock_generate:
        mock_generate.return_value.text = """
        ## calculate_discount
        
        Calculate the discounted price based on a percentage discount.
        
        ### Parameters
        - `price` (float): Original price
        - `percentage` (float): Discount percentage (0-100)
        
        ### Returns
        - `float`: Price after discount applied
        
        ### Example
        ```python
        discounted = calculate_discount(100, 20)  # Returns 80.0
        ```
        """
        
        doc = await gemini_client.generate_documentation(code, analysis)
        
        assert "calculate_discount" in doc
        assert "Parameters" in doc
        assert "Example" in doc
```

### End-to-End Testing

```typescript
// tests/e2e/analysis-flow.test.ts
import { test, expect } from '@playwright/test';
import { createTestRepository, cleanupTestData } from './helpers';

test.describe('Repository Analysis Flow', () => {
  let repoId: string;
  
  test.beforeAll(async () => {
    // Create test repository
    repoId = await createTestRepository();
  });
  
  test.afterAll(async () => {
    // Cleanup
    await cleanupTestData(repoId);
  });
  
  test('should analyze repository end-to-end', async ({ page, request }) => {
    // Start analysis
    const analysisResponse = await request.post('/api/v1/analyze', {
      data: {
        repository_url: `https://github.com/test/${repoId}`,
        branch: 'main',
      },
      headers: {
        'Authorization': `Bearer ${process.env.TEST_TOKEN}`,
      },
    });
    
    expect(analysisResponse.ok()).toBeTruthy();
    const { analysis_id } = await analysisResponse.json();
    
    // Poll for completion
    let analysis;
    for (let i = 0; i < 60; i++) {
      const statusResponse = await request.get(
        `/api/v1/analysis/${analysis_id}`,
        {
          headers: {
            'Authorization': `Bearer ${process.env.TEST_TOKEN}`,
          },
        }
      );
      
      analysis = await statusResponse.json();
      
      if (analysis.status === 'completed') {
        break;
      }
      
      await page.waitForTimeout(5000);
    }
    
    expect(analysis.status).toBe('completed');
    expect(analysis.statistics.files_analyzed).toBeGreaterThan(0);
    expect(analysis.statistics.patterns_detected).toBeGreaterThan(0);
    
    // Test pattern detection
    const patternsResponse = await request.get(
      `/api/v1/patterns?repository_id=${repoId}`,
      {
        headers: {
          'Authorization': `Bearer ${process.env.TEST_TOKEN}`,
        },
      }
    );
    
    const { patterns } = await patternsResponse.json();
    expect(patterns.length).toBeGreaterThan(0);
    
    // Test natural language query
    const queryResponse = await request.post('/api/v1/query', {
      data: {
        query: 'What design patterns are used in this codebase?',
        repository_id: repoId,
      },
      headers: {
        'Authorization': `Bearer ${process.env.TEST_TOKEN}`,
      },
    });
    
    const queryResult = await queryResponse.json();
    expect(queryResult.answer).toContain('pattern');
    expect(queryResult.confidence).toBeGreaterThan(0.7);
  });
});
```

### Load Testing

```javascript
// tests/load/k6-analysis-load.js
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

const errorRate = new Rate('errors');

export const options = {
  stages: [
    { duration: '2m', target: 100 },   // Ramp up to 100 users
    { duration: '5m', target: 100 },   // Stay at 100 users
    { duration: '2m', target: 200 },   // Ramp up to 200 users
    { duration: '5m', target: 200 },   // Stay at 200 users
    { duration: '2m', target: 0 },     // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests under 2s
    errors: ['rate<0.05'],             // Error rate under 5%
  },
};

const BASE_URL = 'https://api.ccl.dev/v1';
const API_KEY = __ENV.LOAD_TEST_API_KEY;

export default function () {
  // Test repository analysis
  const analysisPayload = JSON.stringify({
    repository_url: 'https://github.com/kubernetes/kubernetes',
    incremental: true,
  });
  
  const analysisParams = {
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': API_KEY,
    },
  };
  
  const analysisResponse = http.post(
    `${BASE_URL}/analyze`,
    analysisPayload,
    analysisParams
  );
  
  check(analysisResponse, {
    'analysis started': (r) => r.status === 202,
  });
  
  errorRate.add(analysisResponse.status !== 202);
  
  sleep(1);
  
  // Test pattern query
  const queryPayload = JSON.stringify({
    query: 'What authentication patterns are used?',
    repository_id: 'test-repo-id',
  });
  
  const queryResponse = http.post(
    `${BASE_URL}/query`,
    queryPayload,
    analysisParams
  );
  
  check(queryResponse, {
    'query successful': (r) => r.status === 200,
    'response time OK': (r) => r.timings.duration < 2000,
  });
  
  errorRate.add(queryResponse.status !== 200);
  
  sleep(2);
}
```

---

## Performance Optimization

### Caching Strategy Implementation

```go
// packages/ccl-core/cache/cache.go
package cache

import (
    "context"
    "encoding/json"
    "fmt"
    "time"
    
    "github.com/go-redis/redis/v8"
    "github.com/vmihailenco/msgpack/v5"
)

type CacheManager struct {
    redis   *redis.Client
    local   *LocalCache
    metrics *CacheMetrics
}

func NewCacheManager(redisAddr string) *CacheManager {
    rdb := redis.NewClient(&redis.Options{
        Addr:         redisAddr,
        PoolSize:     100,
        MinIdleConns: 10,
        MaxRetries:   3,
    })
    
    return &CacheManager{
        redis:   rdb,
        local:   NewLocalCache(1000), // 1000 items max
        metrics: NewCacheMetrics(),
    }
}

// Multi-level cache get
func (c *CacheManager) Get(ctx context.Context, key string, dest interface{}) error {
    // Try L1 cache (local)
    if data, found := c.local.Get(key); found {
        c.metrics.RecordHit("l1")
        return msgpack.Unmarshal(data.([]byte), dest)
    }
    
    // Try L2 cache (Redis)
    data, err := c.redis.Get(ctx, key).Bytes()
    if err == nil {
        c.metrics.RecordHit("l2")
        
        // Populate L1
        c.local.Set(key, data, 5*time.Minute)
        
        return msgpack.Unmarshal(data, dest)
    }
    
    if err != redis.Nil {
        return fmt.Errorf("redis error: %w", err)
    }
    
    c.metrics.RecordMiss()
    return ErrCacheMiss
}

// Multi-level cache set
func (c *CacheManager) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
    data, err := msgpack.Marshal(value)
    if err != nil {
        return fmt.Errorf("marshal error: %w", err)
    }
    
    // Set in L2 (Redis)
    if err := c.redis.Set(ctx, key, data, ttl).Err(); err != nil {
        return fmt.Errorf("redis set error: %w", err)
    }
    
    // Set in L1 (local)
    localTTL := ttl
    if localTTL > 5*time.Minute {
        localTTL = 5 * time.Minute
    }
    c.local.Set(key, data, localTTL)
    
    return nil
}

// Pattern-based cache invalidation
func (c *CacheManager) InvalidatePattern(ctx context.Context, pattern string) error {
    // Invalidate Redis keys
    iter := c.redis.Scan(ctx, 0, pattern, 0).Iterator()
    var keys []string
    
    for iter.Next(ctx) {
        keys = append(keys, iter.Val())
    }
    
    if err := iter.Err(); err != nil {
        return err
    }
    
    if len(keys) > 0 {
        if err := c.redis.Del(ctx, keys...).Err(); err != nil {
            return err
        }
    }
    
    // Invalidate local cache
    c.local.InvalidatePattern(pattern)
    
    return nil
}

// Cache key generation helpers
type CacheKeys struct{}

func (k CacheKeys) RepositoryAnalysis(repoID string, version int) string {
    return fmt.Sprintf("repo:analysis:%s:v%d", repoID, version)
}

func (k CacheKeys) Pattern(patternID string) string {
    return fmt.Sprintf("pattern:%s", patternID)
}

func (k CacheKeys) QueryResult(queryHash, contextHash string) string {
    return fmt.Sprintf("query:%s:ctx:%s", queryHash, contextHash)
}

func (k CacheKeys) UserPreferences(userID string) string {
    return fmt.Sprintf("user:pref:%s", userID)
}
```

### Query Optimization

```python
# packages/ccl-core/src/optimization/query_optimizer.py
from typing import List, Dict, Any
import hashlib
import asyncio
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor

@dataclass
class OptimizedQuery:
    original_query: str
    optimized_query: str
    cache_key: str
    estimated_cost: float
    execution_plan: Dict[str, Any]

class QueryOptimizer:
    def __init__(self, cache_manager, spanner_client):
        self.cache = cache_manager
        self.spanner = spanner_client
        self.executor = ThreadPoolExecutor(max_workers=10)
        
    async def optimize_pattern_query(
        self,
        repository_id: str,
        pattern_type: Optional[str] = None,
        min_confidence: float = 0.7
    ) -> OptimizedQuery:
        """Optimize pattern queries for performance"""
        
        # Generate cache key
        cache_key = self._generate_cache_key({
            "repo": repository_id,
            "type": pattern_type,
            "confidence": min_confidence
        })
        
        # Check if we have cached results
        cached = await self.cache.get(cache_key)
        if cached:
            return OptimizedQuery(
                original_query="CACHED",
                optimized_query="CACHED",
                cache_key=cache_key,
                estimated_cost=0.0,
                execution_plan={"source": "cache"}
            )
        
        # Build optimized query
        query = """
        SELECT 
            pattern_id,
            pattern_type,
            pattern_name,
            confidence,
            occurrences,
            files,
            template
        FROM patterns@{FORCE_INDEX=idx_patterns_confidence}
        WHERE repository_id = @repository_id
        """
        
        params = {"repository_id": repository_id}
        
        if pattern_type:
            query += " AND pattern_type = @pattern_type"
            params["pattern_type"] = pattern_type
            
        query += f" AND confidence >= {min_confidence}"
        query += " ORDER BY confidence DESC, occurrences DESC"
        query += " LIMIT 100"
        
        # Get query execution plan
        plan = await self._get_execution_plan(query, params)
        
        return OptimizedQuery(
            original_query=query,
            optimized_query=query,
            cache_key=cache_key,
            estimated_cost=plan.get("estimated_cost", 1.0),
            execution_plan=plan
        )
    
    async def optimize_similarity_search(
        self,
        embedding: np.ndarray,
        repository_id: str,
        top_k: int = 10
    ) -> List[Dict]:
        """Optimize vector similarity search"""
        
        # Use approximate nearest neighbor search
        loop = asyncio.get_event_loop()
        
        # Parallel search across shards
        tasks = []
        for shard in range(self.get_shard_count()):
            task = loop.run_in_executor(
                self.executor,
                self._search_shard,
                embedding,
                repository_id,
                shard,
                top_k * 2  # Over-fetch for better results
            )
            tasks.append(task)
        
        # Gather results from all shards
        shard_results = await asyncio.gather(*tasks)
        
        # Merge and re-rank results
        all_results = []
        for results in shard_results:
            all_results.extend(results)
        
        # Sort by similarity score
        all_results.sort(key=lambda x: x["similarity"], reverse=True)
        
        return all_results[:top_k]
    
    def _generate_cache_key(self, params: Dict) -> str:
        """Generate deterministic cache key"""
        
        # Sort parameters for consistency
        sorted_params = sorted(params.items())
        param_str = str(sorted_params)
        
        # Generate hash
        return hashlib.sha256(param_str.encode()).hexdigest()
    
    async def _get_execution_plan(
        self,
        query: str,
        params: Dict
    ) -> Dict[str, Any]:
        """Get query execution plan from Spanner"""
        
        with self.spanner.snapshot() as snapshot:
            result = snapshot.execute_sql(
                f"EXPLAIN {query}",
                params=params,
                query_mode=spanner.QueryMode.PROFILE
            )
            
            # Parse execution plan
            plan = {
                "estimated_cost": 1.0,
                "estimated_rows": 0,
                "operations": []
            }
            
            for row in result:
                plan["operations"].append({
                    "type": row[0],
                    "cost": row[1],
                    "rows": row[2]
                })
                plan["estimated_cost"] += row[1]
                plan["estimated_rows"] += row[2]
            
            return plan
```

### Resource Pooling

```go
// packages/ccl-core/pool/worker_pool.go
package pool

import (
    "context"
    "fmt"
    "sync"
    "time"
)

type WorkerPool struct {
    workers    int
    tasks      chan Task
    results    chan Result
    errors     chan error
    wg         sync.WaitGroup
    ctx        context.Context
    cancel     context.CancelFunc
    metrics    *PoolMetrics
}

type Task interface {
    ID() string
    Execute(ctx context.Context) (interface{}, error)
    Priority() int
}

type Result struct {
    TaskID   string
    Data     interface{}
    Duration time.Duration
    Error    error
}

func NewWorkerPool(workers int) *WorkerPool {
    ctx, cancel := context.WithCancel(context.Background())
    
    return &WorkerPool{
        workers: workers,
        tasks:   make(chan Task, workers*10),
        results: make(chan Result, workers*10),
        errors:  make(chan error, workers),
        ctx:     ctx,
        cancel:  cancel,
        metrics: NewPoolMetrics(),
    }
}

func (p *WorkerPool) Start() {
    for i := 0; i < p.workers; i++ {
        p.wg.Add(1)
        go p.worker(i)
    }
    
    // Metrics collector
    go p.collectMetrics()
}

func (p *WorkerPool) worker(id int) {
    defer p.wg.Done()
    
    for {
        select {
        case task, ok := <-p.tasks:
            if !ok {
                return
            }
            
            start := time.Now()
            p.metrics.RecordTaskStart(task.ID())
            
            // Execute task with timeout
            ctx, cancel := context.WithTimeout(p.ctx, 30*time.Second)
            data, err := task.Execute(ctx)
            cancel()
            
            duration := time.Since(start)
            
            result := Result{
                TaskID:   task.ID(),
                Data:     data,
                Duration: duration,
                Error:    err,
            }
            
            select {
            case p.results <- result:
                p.metrics.RecordTaskComplete(task.ID(), duration, err)
            case <-p.ctx.Done():
                return
            }
            
        case <-p.ctx.Done():
            return
        }
    }
}

func (p *WorkerPool) Submit(task Task) error {
    select {
    case p.tasks <- task:
        p.metrics.RecordTaskSubmitted(task.ID())
        return nil
    case <-p.ctx.Done():
        return fmt.Errorf("pool is shutting down")
    default:
        return fmt.Errorf("task queue is full")
    }
}

func (p *WorkerPool) GetResult() (Result, bool) {
    select {
    case result := <-p.results:
        return result, true
    default:
        return Result{}, false
    }
}

func (p *WorkerPool) Shutdown(timeout time.Duration) error {
    // Stop accepting new tasks
    close(p.tasks)
    
    // Wait for workers to finish
    done := make(chan struct{})
    go func() {
        p.wg.Wait()
        close(done)
    }()
    
    select {
    case <-done:
        p.cancel()
        return nil
    case <-time.After(timeout):
        p.cancel()
        return fmt.Errorf("shutdown timeout exceeded")
    }
}

// Analysis task implementation
type AnalysisTask struct {
    id         string
    repository string
    filePath   string
    content    string
    language   string
    priority   int
    analyzer   CodeAnalyzer
}

func (t *AnalysisTask) ID() string {
    return t.id
}

func (t *AnalysisTask) Priority() int {
    return t.priority
}

func (t *AnalysisTask) Execute(ctx context.Context) (interface{}, error) {
    // Check context cancellation
    select {
    case <-ctx.Done():
        return nil, ctx.Err()
    default:
    }
    
    // Perform analysis
    analysis, err := t.analyzer.AnalyzeFile(
        t.filePath,
        t.content,
        t.language
    )
    
    if err != nil {
        return nil, fmt.Errorf("analysis failed: %w", err)
    }
    
    return analysis, nil
}
```

---

## Deployment Procedures

### Infrastructure as Code

```hcl
# infrastructure/terraform/main.tf
terraform {
  required_version = ">= 1.6.0"
  
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "~> 5.0"
    }
  }
  
  backend "gcs" {
    bucket = "ccl-terraform-state"
    prefix = "prod"
  }
}

# Provider configuration
provider "google" {
  project = var.project_id
  region  = var.region
}

provider "google-beta" {
  project = var.project_id
  region  = var.region
}

# Variables
variable "project_id" {
  description = "GCP Project ID"
  type        = string
  default     = "ccl-platform-prod"
}

variable "region" {
  description = "Primary region"
  type        = string
  default     = "us-central1"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "prod"
}

# Networking
module "networking" {
  source = "./modules/networking"
  
  project_id   = var.project_id
  region       = var.region
  environment  = var.environment
}

# Security
module "security" {
  source = "./modules/security"
  
  project_id   = var.project_id
  region       = var.region
  environment  = var.environment
  vpc_id       = module.networking.vpc_id
}

# Data layer
module "data" {
  source = "./modules/data"
  
  project_id   = var.project_id
  region       = var.region
  environment  = var.environment
  kms_key_id   = module.security.database_encryption_key
}

# Compute layer
module "compute" {
  source = "./modules/compute"
  
  project_id          = var.project_id
  region              = var.region
  environment         = var.environment
  vpc_id              = module.networking.vpc_id
  subnet_id           = module.networking.subnet_id
  service_account_email = module.security.compute_service_account
}

# AI/ML layer
module "ai" {
  source = "./modules/ai"
  
  project_id   = var.project_id
  region       = var.region
  environment  = var.environment
}

# Monitoring
module "monitoring" {
  source = "./modules/monitoring"
  
  project_id   = var.project_id
  region       = var.region
  environment  = var.environment
}
```

### Cloud Run Service Deployment

```yaml
# infrastructure/cloud-run/analysis-engine.yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: ccl-analysis-engine
  namespace: ccl-prod
  annotations:
    run.googleapis.com/launch-stage: GA
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/execution-environment: gen2
        run.googleapis.com/cpu-throttling: "false"
        autoscaling.knative.dev/minScale: "5"
        autoscaling.knative.dev/maxScale: "1000"
        run.googleapis.com/vpc-access-connector: ccl-vpc-connector
        run.googleapis.com/vpc-access-egress: private-ranges-only
    spec:
      serviceAccountName: <EMAIL>
      containerConcurrency: 100
      timeoutSeconds: 900
      containers:
      - image: gcr.io/ccl-platform-prod/analysis-engine:latest
        ports:
        - containerPort: 8080
          protocol: TCP
        env:
        - name: PROJECT_ID
          value: ccl-platform-prod
        - name: SPANNER_INSTANCE
          value: ccl-main
        - name: SPANNER_DATABASE
          value: analysis
        - name: REDIS_HOST
          value: ********:6379
        - name: LOG_LEVEL
          value: info
        resources:
          limits:
            cpu: "4"
            memory: 4Gi
          requests:
            cpu: "2"
            memory: 2Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
```

### CI/CD Pipeline

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  workflow_dispatch:

env:
  PROJECT_ID: ccl-platform-prod
  REGION: us-central1

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'
          
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          cache: 'pip'
          
      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          
      - name: Install dependencies
        run: make install-deps
        
      - name: Run tests
        run: make test
        
      - name: Run security scan
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          
  build:
    needs: test
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service:
          - analysis-engine
          - query-intelligence
          - pattern-mining
          - marketplace
          - gateway
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Google Cloud
        uses: google-github-actions/setup-gcloud@v1
        with:
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          project_id: ${{ env.PROJECT_ID }}
          
      - name: Configure Docker
        run: gcloud auth configure-docker
        
      - name: Build and push image
        run: |
          docker build -t gcr.io/$PROJECT_ID/${{ matrix.service }}:$GITHUB_SHA \
            -f services/${{ matrix.service }}/Dockerfile \
            services/${{ matrix.service }}
          
          docker push gcr.io/$PROJECT_ID/${{ matrix.service }}:$GITHUB_SHA
          
      - name: Tag as latest
        run: |
          docker tag gcr.io/$PROJECT_ID/${{ matrix.service }}:$GITHUB_SHA \
            gcr.io/$PROJECT_ID/${{ matrix.service }}:latest
          
          docker push gcr.io/$PROJECT_ID/${{ matrix.service }}:latest
  
  deploy:
    needs: build
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service:
          - analysis-engine
          - query-intelligence
          - pattern-mining
          - marketplace
          - gateway
          
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Google Cloud
        uses: google-github-actions/setup-gcloud@v1
        with:
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          project_id: ${{ env.PROJECT_ID }}
          
      - name: Deploy to Cloud Run
        run: |
          gcloud run deploy ${{ matrix.service }} \
            --image gcr.io/$PROJECT_ID/${{ matrix.service }}:$GITHUB_SHA \
            --region $REGION \
            --platform managed \
            --no-traffic
            
      - name: Run integration tests
        run: |
          SERVICE_URL=$(gcloud run services describe ${{ matrix.service }} \
            --region $REGION \
            --format 'value(status.url)')
          
          make integration-test SERVICE=${{ matrix.service }} URL=$SERVICE_URL
          
      - name: Migrate traffic
        if: success()
        run: |
          gcloud run services update-traffic ${{ matrix.service }} \
            --region $REGION \
            --to-latest
            
  post-deploy:
    needs: deploy
    runs-on: ubuntu-latest
    steps:
      - name: Notify success
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: 'Production deployment successful!'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          
      - name: Update status page
        run: |
          curl -X POST https://api.statuspage.io/v1/pages/$PAGE_ID/incidents \
            -H "Authorization: OAuth ${{ secrets.STATUSPAGE_TOKEN }}" \
            -d '{
              "incident": {
                "name": "Deployment completed",
                "status": "resolved",
                "component_ids": ["all"]
              }
            }'
```

### Monitoring Setup

```yaml
# infrastructure/monitoring/alerts.yaml
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: ccl-alerts
  namespace: ccl-monitoring
spec:
  groups:
    - name: availability
      interval: 30s
      rules:
        - alert: ServiceDown
          expr: up{job=~"ccl-.*"} == 0
          for: 2m
          labels:
            severity: critical
            team: platform
          annotations:
            summary: "Service {{ $labels.job }} is down"
            description: "{{ $labels.job }} has been down for more than 2 minutes"
            
        - alert: HighErrorRate
          expr: |
            rate(http_requests_total{status=~"5.."}[5m]) 
            / rate(http_requests_total[5m]) > 0.05
          for: 5m
          labels:
            severity: warning
            team: platform
          annotations:
            summary: "High error rate on {{ $labels.service }}"
            description: "Error rate is {{ $value | humanizePercentage }}"
            
        - alert: HighLatency
          expr: |
            histogram_quantile(0.95, 
              rate(http_request_duration_seconds_bucket[5m])
            ) > 2
          for: 5m
          labels:
            severity: warning
            team: platform
          annotations:
            summary: "High latency on {{ $labels.service }}"
            description: "P95 latency is {{ $value }}s"
            
    - name: resources
      interval: 1m
      rules:
        - alert: HighMemoryUsage
          expr: |
            container_memory_working_set_bytes
            / container_spec_memory_limit_bytes > 0.9
          for: 5m
          labels:
            severity: warning
            team: platform
          annotations:
            summary: "High memory usage in {{ $labels.container }}"
            description: "Memory usage is {{ $value | humanizePercentage }}"
            
        - alert: HighCPUUsage
          expr: |
            rate(container_cpu_usage_seconds_total[5m]) > 0.9
          for: 5m
          labels:
            severity: warning
            team: platform
          annotations:
            summary: "High CPU usage in {{ $labels.container }}"
            description: "CPU usage is {{ $value | humanizePercentage }}"
            
    - name: business
      interval: 5m
      rules:
        - alert: LowAnalysisSuccessRate
          expr: |
            rate(analysis_completed_total[1h])
            / rate(analysis_started_total[1h]) < 0.9
          for: 15m
          labels:
            severity: warning
            team: product
          annotations:
            summary: "Low analysis success rate"
            description: "Only {{ $value | humanizePercentage }} of analyses are completing successfully"
            
        - alert: HighQueryLatency
          expr: |
            histogram_quantile(0.95, 
              rate(query_duration_seconds_bucket[5m])
            ) > 3
          for: 10m
          labels:
            severity: critical
            team: ml
          annotations:
            summary: "Query response time degraded"
            description: "P95 query latency is {{ $value }}s"
            
        - alert: PatternDetectionFailure
          expr: |
            rate(pattern_detection_errors_total[5m]) > 0.1
          for: 5m
          labels:
            severity: warning
            team: ml
          annotations:
            summary: "Pattern detection errors increasing"
            description: "{{ $value }} errors per second"

# Grafana Dashboard Configuration
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ccl-dashboard
  namespace: ccl-monitoring
data:
  dashboard.json: |
    {
      "dashboard": {
        "title": "CCL Platform Overview",
        "panels": [
          {
            "title": "Request Rate",
            "targets": [
              {
                "expr": "sum(rate(http_requests_total[5m])) by (service)"
              }
            ]
          },
          {
            "title": "Error Rate",
            "targets": [
              {
                "expr": "sum(rate(http_requests_total{status=~\"5..\"}[5m])) by (service)"
              }
            ]
          },
          {
            "title": "Analysis Pipeline",
            "targets": [
              {
                "expr": "rate(analysis_started_total[5m])"
              },
              {
                "expr": "rate(analysis_completed_total[5m])"
              }
            ]
          },
          {
            "title": "Query Performance",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, rate(query_duration_seconds_bucket[5m]))"
              }
            ]
          }
        ]
      }
    }
```

---

## Conclusion

This technical specification provides a comprehensive guide for implementing CCL as a full-scale, production-ready platform from day one. By leveraging Google Cloud's advanced services and following cloud-native best practices, we can deliver a robust, scalable, and intelligent codebase understanding platform.

The key to successful implementation is:
1. **Parallel Development**: Multiple teams working on different services simultaneously
2. **Continuous Integration**: Automated testing and deployment from the start
3. **Observability First**: Comprehensive monitoring and logging built-in
4. **Security by Design**: Zero-trust architecture and encryption everywhere
5. **Performance Optimization**: Multi-level caching and intelligent query optimization

With this specification, the development team has everything needed to build CCL as a world-class platform that transforms how developers understand and interact with code.